<template>
  <div class="wrapper">
    <image
      class="icon"
      src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_search.png"
    ></image>
    <swiper
      class="swiper"
      loop="{{false}}"
      indicator="{{false}}"
      vertical="{{true}}"
      autoplay="{{true}}"
      enableswipe="{{false}}"
    >
      <div
        class="swiper__item"
        for="item in list"
        tid="$idx"
        @click="handleClickItem($idx)"
      >
        <text class="swiper__item__text">
          {{ item.text }}
        </text>
      </div>
    </swiper>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [{ text: '重生之我在诸王峡谷捡垃圾' }, { text: '小说1' }],
    }
  },

  handleClickItem(index) {
    this.$emit('clickItem', {
      index,
      item: this.list[index],
    })
  },
}
</script>

<style lang="less">
.wrapper {
  background-color: #f7f7f7;
  width: 100%;
  height: 88px;
  border-radius: 20px;
  padding: 0 20px;
  align-items: center;
}

.icon {
  width: 48px;
  height: 48px;
  margin-right: 8px;
}

.swiper {
  &__item {
    &__text {
      font-size: 28px;
      color: #999999;
    }
  }
}
</style>
