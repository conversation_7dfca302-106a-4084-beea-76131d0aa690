<import name="nav-header" src="./nav-header.ux"></import>
<import name="search-input" src="./search-input.ux"></import>
<import name="spannable-text" src="./spannable-text.ux"></import>
<import name="novel-card" src="./novel/novel-card.ux"></import>

<template>
  <div class="page">
    <slot name="header">
      <nav-header></nav-header>
    </slot>
    <div style="margin-top: 20px; width: 100%">
      <search-input></search-input>
    </div>
    <list class="container">
      <!--默认-->
      <list-item if="{{false}}" type="default-type" class="list-item">
        <div class="hot-history">
          <div class="hot-history__header">
            <text class="hot-history__header__title">搜索热词</text>
          </div>
          <div class="hot-history__list">
            <text class="hot-history__list__item" for="item in 9">角色</text>
          </div>
        </div>
        <div class="hot-history">
          <div class="hot-history__header">
            <text class="hot-history__header__title">搜索历史</text>
            <image
              class="hot-history__header__del"
              src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_del.webp"
            ></image>
          </div>
          <div class="hot-history__list">
            <text class="hot-history__list__item" for="item in 9">
              武侠修仙
            </text>
          </div>
        </div>
        <div class="hot-rank">
          <text class="hot-rank__title">热搜榜单</text>
          <div class="hot-rank__list">
            <div
              class="hot-rank__list__item hot-rank__list__item--{{$idx + 1}}"
              for="item in 6"
              tid="$idx"
            >
              <image
                class="hot-rank__list__item__cover"
                src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/logo.png"
              ></image>
              <div style="flex-direction: column">
                <text class="hot-rank__list__item__title">开局卖大力</text>
                <div>
                  <image
                    if="{{$idx < 3}}"
                    class="hot-rank__list__item__hot-icon"
                    src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_hot.webp"
                  ></image>
                  <text class="hot-rank__list__item__popularity">
                    111万人气
                  </text>
                </div>
              </div>
            </div>
          </div>
        </div>
      </list-item>
      <!--联想词-->
      <list-item if="{{false}}" type="relevance" class="list-item">
        <div class="relevance-list">
          <div
            class="relevance-list__item"
            for="item in relevanceList"
            tid="$idx"
          >
            <image
              if='{{item.type === "author"}}'
              class="relevance-list__item__icon"
              src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_mine.webp"
            ></image>
            <image
              else
              class="relevance-list__item__icon"
              src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_book.webp"
            ></image>
            <text class="relevance-list__item__text">
              {{ item.type === 'author' ? item.author : item.book }}
            </text>
            <text
              if='{{item.type === "author"}}'
              class="relevance-list__item__tag"
            >
              作者
            </text>
          </div>
        </div>
      </list-item>
      <!--详情-->
      <list-item type="detail" class="list-item">
        <div class="author-card">
          <div class="author-card__header">
            <image
              class="author-card__header__icon"
              src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_mine.webp"
            ></image>
            <spannable-text
              content="什么小说什么小"
              span-content="小说"
              @click-span="handleClickSpan"
              span-style="color: red"
              text-style="font-size: 32px; color: #666666;"
            ></spannable-text>
            <text class="author-card__header__tag">作者</text>
            <div class="author-card__header__right">
              <text>详情</text>
              <image
                src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_arrow_right.webp"
              ></image>
            </div>
          </div>
          <text class="author-card__works">作品《日常系顶级》</text>
        </div>
        <div class="novel-list">
          <novel-card
            for="item in novelList"
            tid="$idx"
            title="{{item.title}}"
            desc="{{item.desc}}"
            cover='"item.cover'
          >
            <spannable-text
              slot="other"
              content="什么小说什么小"
              span-content="小说"
              @click-span="handleClickSpan"
              text-style="font-size: 24px; color: #292938;"
            ></spannable-text>
          </novel-card>
        </div>
      </list-item>
    </list>
  </div>
</template>

<script>
import { showToast } from '@quickapp/utils'

export default {
  data() {
    return {
      relevanceList: [
        {
          type: 'author',
          author: '狂奔的蜗牛',
          book: '开局卖大力',
        },
        {
          type: 'book',
          author: '狂奔的蜗牛',
          book: '开局卖大力',
        },
      ],
      novelList: Array(10).fill({
        cover: '/assets/images/1.webp',
        title: '开机卖大力',
        desc: '地球进入灵气复苏时代，人类开启异能觉醒！江南开局觉醒最强地摊系…',
      }),
    }
  },
  handleClickSpan({ detail }) {
    showToast(detail.text)
  },
  test(msg) {
    showToast(msg)
  },
}
</script>

<style lang="less">
.page {
  flex-direction: column;
  padding-top: 88px;
  height: 100%;
}

.container {
  flex: 1;
}

.list-item {
  flex-direction: column;
}

.hot-history {
  flex-direction: column;
  padding: 0 30px;
  margin-top: 40px;

  &__header {
    align-items: center;

    &__title {
      font-size: 28px;
      font-weight: 400;
      color: #999999;
    }

    &__del {
      width: 32px;
      height: 32px;
      margin-left: auto;
    }
  }

  &__list {
    flex-wrap: wrap;

    &__item {
      height: 52px;
      padding: 0 32px;
      background-color: #f7f7f7;
      border-radius: 26px;
      margin-right: 30px;
      margin-top: 30px;
    }
  }
}

.hot-rank {
  flex-direction: column;
  padding: 0 30px;
  margin-top: 60px;
  margin-bottom: 60px;

  &__title {
    font-size: 28px;
    color: #999999;
    line-height: 40px;
  }

  &__list {
    flex-direction: column;

    &__item {
      align-items: center;
      background-color: #fafafa;
      border-radius: 10px;
      margin-top: 30px;

      &--1 {
        background-color: rgba(255, 232, 232, 0.5);
      }
      &--2 {
        background-color: rgba(255, 231, 212, 0.5);
      }
      &--3 {
        background-color: rgba(254, 239, 219, 0.3);
      }

      &__cover {
        width: 124px;
        height: 160px;
        border-radius: 9px;
        margin-right: 28px;
      }

      &__title {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        line-height: 32px;
        margin-bottom: 20px;
      }

      &__hot-icon {
        width: 28px;
        height: 28px;
        margin-right: 10px;
      }

      &__popularity {
        font-size: 24px;
        font-weight: bold;
        color: #999999;
      }
    }
  }
}

.relevance-list {
  flex-direction: column;
  padding: 0 60px;
  margin-bottom: 60px;

  &__item {
    margin-top: 52px;
    align-items: center;

    &__icon {
      width: 28px;
      height: 32px;
      margin-right: 16px;
    }

    &__text {
      font-size: 32px;
      color: #666666;
    }

    &__tag {
      text-align: center;
      padding: 0 6px;
      height: 36px;
      border-radius: 4px;
      font-size: 24px;
      color: #666666;
      border: 2px solid rgba(187, 187, 187, 0.5);
      margin-left: 16px;
    }
  }
}

.author-card {
  flex-direction: column;
  padding: 50px 60px 40px;
  border-bottom: 20px solid rgba(247, 247, 247, 1);

  &__header {
    align-items: center;

    &__icon {
      width: 28px;
      height: 32px;
      margin-right: 16px;
    }

    &__tag {
      text-align: center;
      width: 60px;
      height: 36px;
      border-radius: 4px;
      color: #666666;
      font-size: 24px;
      margin-left: 20px;
      border: 2px solid rgba(187, 187, 187, 0.5);
    }

    &__right {
      margin-left: auto;
      align-items: center;

      text {
        font-size: 24px;
        font-weight: bold;
        color: #bbbbbb;
        margin-right: -10px;
      }
      image {
        width: 56px;
        height: 56px;
      }
    }
  }

  &__works {
    margin-left: 50px;
    font-size: 26px;
    color: #666666;
    margin-top: 20px;
  }
}

.novel-list {
  flex-direction: column;
  padding: 0 30px 40px;
}
</style>
