<template>
  <div class="wrapper" style="margin-top: {{marginTop}}">
    <image class="wrapper__cover" src="{{ cover }}"></image>
    <div class="wrapper__info">
      <text class="wrapper__info__title">{{ title }}</text>
      <text class="wrapper__info__desc">{{ desc }}</text>
      <div class="wrapper__info__other">
        <slot name="other">
          <text>{{ other }}</text>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    cover: {
      default: '/assets/images/1.webp',
    },
    title: {
      default: '标题',
    },
    desc: {
      default: '描述',
    },
    other: {
      default: '其他信息',
    },
    marginTop: {
      default: '30px',
    },
  },
}
</script>

<style lang="less">
.wrapper {
  padding: 4px 0 6px;

  &__cover {
    width: 140px;
    height: 180px;
    border-radius: 10px;
    margin-right: 20px;
  }

  &__info {
    flex: 1;
    flex-direction: column;

    &__title {
      font-size: 28px;
      font-weight: bold;
      color: #333333;
    }

    &__desc {
      font-size: 24px;
      margin-top: 24px;
      color: rgba(41, 41, 56, 0.7);
      line-height: 36px;
      lines: 2;
      text-overflow: ellipsis;
    }

    &__other {
      margin-top: auto;
      text {
        font-size: 24px;
        font-weight: 400;
        color: rgba(41, 41, 56, 0.4);
      }
    }
  }
}
</style>
