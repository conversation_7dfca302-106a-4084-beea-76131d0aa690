<import name="table-content" src="./table-content.ux"></import>
<import name="novel-setting" src="./novel-setting.ux"></import>
<import name="apx-spin" src="@quickapp/apex-ui/components/spin/index"></import>
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>
<!-- <import
  name="nativebanner"
  src="@quickapp/business/lib/nativebanner.ux"
></import> -->
<!-- <import
  name="nativead"
  src="@quickapp/business/components/nativead.ux"
></import> -->
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>

<template>
  <div>
    <service></service>
    <div
      id="drawer"
      class="page"
      @click="handleClick"
      style="background-color: {{currentTheme.bgColor}}; padding-top: {{topHeight}}px"
    >
      <div
        class="page__header"
        style="background-color: {{currentTheme.bgColor}}; height: {{topHeight}}px"
      >
        <text
          class="page__header__back-btn"
          style="color: {{currentTheme.frontColor}}"
          @click="handleBackPress"
        >
          退出阅读
        </text>
        <text
          class="page__header__title"
          style="color: {{currentTheme.frontColor}}"
        >
          {{ currentPage && currentPage.bookName ? currentPage.bookName : '' }}
        </text>
      </div>
      <apx-spin
        class="spin-box"
        tip="加载中..."
        nested="{{true}}"
        loading="{{loading}}"
      >
        <swiper
          show="{{!loading}}"
          id="swiper"
          slot="nested"
          class="swiper"
          index="{{swiperOpt.index}}"
          indicator="{{swiperOpt.indicator}}"
          loop="{{swiperOpt.loop}}"
          enableswipe="{{swiperOpt.canSwipe}}"
          @change="handleChange"
          style="background-color: {{currentTheme.bgColor}}"
        >
          <block for="pageContent in list">
            <div
              if='{{pageContent.type === "content"}}'
              class="swiper-item"
              tid="$idx"
              style="padding-left: {{fontSize}}px;"
            >
              <text
                for="content in pageContent.content"
                tid="$idx"
                style="{{contentTextStyle}}"
              >
                {{ content }}
              </text>
            </div>
            <div else class="swiper-item-ad">
              <!-- <nativead
                if="{{$idx === swiperIndex}}"
                id="{{'ad_essay_' + $idx}}"
                event-name="ad_essay"
                adid="native_ad2"
                onload="handleAdLoad"
                onclick="handleAdClick"
                ad-style="{{adStyle}}"
              >
                <div slot="header" class="swiper-item-ad-header">
                  <text class="swiper-item-ad-title">内容免费公告</text>
                  <text class="swiper-item-ad-text">
                    亲爱的用户，您正在阅读正版免费内容。阅读中出现的广告内容是为了激励作者，给您提供更多优质的免费内容。感谢您的理解。
                  </text>
                </div>
              </nativead> -->
              <text class="swiper-item-ad-tip">👈🏻 向左滑动阅读正文</text>
            </div>
          </block>
        </swiper>
      </apx-spin>

      <div
        show="showBannerAd"
        class="banner-ad"
        style="height: {{bannerHeight}}px; background-color: {{currentTheme.bgColor}};"
      >
        <!-- <nativebanner id="banner" event-name="ad_essay_bottom"></nativebanner> -->
      </div>
      <div if="showSetting" class="setting-box">
        <novel-setting
          @change-font="handleChangeFont"
          @change-theme="handleChangeColor"
          @click-table-content="openDrawer"
          @click-next="nextChapterContent"
          @click-prev="preChapterContent"
          font-size="{{currentFontSize}}"
          theme="{{currentTheme}}"
          has-next="{{currentPage && !!currentPage.nextChapterId}}"
          has-prev="{{currentPage && !!currentPage.preChapterId}}"
        ></novel-setting>
      </div>

      <apex-popup id="popup" position="left">
        <table-content
          id="table-content"
          @click-item="handleClickItem"
        ></table-content>
      </apex-popup>
    </div>
  </div>
</template>

<script>
import { loadReader } from '@quickapp/utils/lib/reader'
import { getChapterContentData } from '@quickapp/utils/lib/api/novel/bookInfo'
import { showToast } from '@quickapp/utils'
import {
  defaultFontSize,
  defaultTheme,
  getLastTimeNNovelChapter,
  getNovelFontSize,
  getNovelTheme,
  setLastTimeNovelChapter,
  setNovelFontSize,
  setNovelTheme,
} from '@quickapp/utils/lib/novel'
import * as utils from '@quickapp/utils'
import device from '@quickapp/business/lib/device'

import router from '@system.router'

export default {
  props: {
    bookId: {
      type: Number,
      // default: 1,
    },
    chapterId: {
      type: Number,
      // default: 1,
    },
    name: {
      type: String,
      // default: '开局买大力',
    },
    toCachePage: {
      type: String,
      // default: '1',
    },
    intervalAdNum: {
      type: Number,
      // default: 4,
    },
  },

  data: {
    swiperOpt: {
      index: 0, // 初始默认值
      indicator: false,
      loop: false,
      canSwipe: true,
    },
    list: [],
    fontSize: 30,
    paddingHorizontal: 80,
    paddingVertical: 40,
    currentPage: {
      //   bookId: null,
      //   chapterId: null,
      //   chapterName: '',
      //   content: '',
      //   cover: '',
      //   introduction: '',
      //   nextChapterId: null,
      //   preChapterId: null,
      //   bookName: null,
    },
    // 最大缓存章节数
    maxLen: 3,
    // 把 chapterList 给 loadReader 生出页数
    chapterList: [],
    // 下一页阈值
    nextThreshold: 3,
    // 上一页阈值
    prevThreshold: 3,
    prevIndex: 0,
    timer: null,
    showSetting: false,
    showIntAd: true,
    currentFontSize: defaultFontSize,
    currentTheme: defaultTheme,
    currentChapter: 1,
    loading: false,
    topHeight: 100,
    bannerHeight: 150,
    showBannerAd: true,
    cachePageNum: -1,
    adClicked: false,
    adStyle: {
      height: '1000px',
    },
    swiperIndex: 0,
  },

  computed: {
    lineHeight() {
      return this.currentFontSize * 2.6
      // return this.currentFontSize * 2
    },
    contentTextStyle() {
      return {
        fontSize: this.currentFontSize + 'px',
        lineHeight: this.lineHeight + 'px',
        color: this.currentTheme.frontColor,
      }
    },
  },

  onInit() {
    const toCachePage = this.toCachePage || '1'

    if (toCachePage === '1') {
      getLastTimeNNovelChapter()
        .then(res => {
          console.log('onInit getLastTimeNNovelChapter', res)
          if (res) {
            const cacheContent = res[this.bookId]
            const cPage =
              cacheContent && cacheContent.currentPage
                ? cacheContent.currentPage
                : 0
            this.cachePageNum = cPage
            this.jumpChapter(this.chapterId, cPage)
          } else {
            this.jumpChapter(this.chapterId)
          }
        })
        .catch(() => {
          this.jumpChapter(this.chapterId)
        })
    } else {
      this.jumpChapter(this.chapterId)
    }

    getNovelFontSize().then(fontSize => {
      this.setFontSize(fontSize)
    })

    getNovelTheme().then(theme => {
      this.setTheme(theme)
    })

    this.$watch('currentFontSize', 'handleChangeFontSize')
    this.adStyle.height = this.$page.windowHeight - 300 + 'px'
  },

  handlePageShow() {
    try {
      // let adComponent = this.$child('ad_essay_' + this.swiperOpt.index)
      let adComponent = this.$child('ad_essay_' + this.swiperIndex)
      if (adComponent) {
        adComponent.reportNativeShow()
      }
    } catch (e) {
      console.error('handlePageShow', e)
    }
    try {
      let adComponent = this.$child('banner')
      if (adComponent) {
        adComponent.reportNativeShow()
      }
    } catch (e) {
      console.error('handlePageShow', e)
    }
  },

  handleBackPress() {
    this.cacheNovelChapter().finally(() => {})
    router.back()
  },

  handleChangeFontSize() {
    // this.renderList(this.swiperOpt.index)
    this.renderList(this.swiperIndex)
  },

  // 重新计算
  reRenderList() {
    // this.renderList(this.swiperOpt.index)
    this.renderList(this.swiperIndex)
  },

  handleCloseBanner() {
    this.bannerHeight = 0
    this.showBannerAd = false
    this.reRenderList()
  },

  renderList(cacheIndex) {
    let list = this.chapterList.reduce((total, currentValue) => {
      return total.concat(this.renderReader(currentValue))
    }, [])
    // list = this.insertAdContainer(list)

    const fIndex = this.currentPage
      ? list.findIndex(
          it =>
            JSON.stringify(it.content) ===
              JSON.stringify(this.currentPage.content) &&
            it.chapterName === this.currentPage.chapterName
        )
      : 0

    const index = cacheIndex || fIndex

    const i = index === -1 ? 0 : index
    this.list = list
    setTimeout(() => {
      this.swiperToPage(i)
    }, 10)
    this.currentPage = list[i]
  },

  cacheNovelChapter() {
    if (this.currentPage) {
      return setLastTimeNovelChapter({
        bookId: this.bookId,
        chapterId: this.currentPage.chapterId,
        chapterName: this.currentPage.chapterName,
        introduction: this.currentPage.introduction,
        // currentPage: this.swiperOpt.index,
        currentPage: this.swiperIndex,
      })
    }

    return Promise.resolve()
  },

  setFontSize(fontSize) {
    this.currentFontSize = fontSize
    setNovelFontSize(fontSize)
  },

  setTheme(theme) {
    this.currentTheme = theme
    setNovelTheme(theme)
  },

  handleChangeFont({ detail }) {
    this.setFontSize(detail.font)
  },

  handleChangeColor({ detail }) {
    this.setTheme(detail.theme)
  },

  /**
   * 上一章
   */
  preChapterContent() {
    this.jumpChapter(this.currentPage.preChapterId)
  },

  handleClickItem({ detail }) {
    // this.$element('drawer').closeDrawer()
    this.$child('popup').hide()
    this.jumpChapter(detail.data.id)
  },

  /**
   * 下一章
   */
  nextChapterContent() {
    this.jumpChapter(this.currentPage.nextChapterId)
  },

  async jumpChapter(chapterId, cacheIndex) {
    if (chapterId) {
      const index = this.list.findIndex(it => it.chapterId === chapterId)
      if (index !== -1) {
        const fIndex = cacheIndex || index
        this.swiperToPage(fIndex)
        console.log('jumpChapter index' + index)
      } else {
        this.loading = true
        console.log('list 之前 ====>>>>', this.list)
        this.list = []
        this.chapterList = []
        console.log('list 之后', this.list)

        const insertAdAndRender = (index, res) => {
          setTimeout(() => {
            this.swiperToPage(index)
            this.loading = false
          }, 300)
        }
        try {
          const res = await this.getData(chapterId, 'next', 0)

          if (res.preChapterId) {
            await this.getData(res.preChapterId, 'prev', 0)
            const index = this.list.findIndex(
              it => it.chapterId === res.chapterId
            )
            const finallyIndex = cacheIndex || index

            insertAdAndRender(finallyIndex, res)
          } else {
            insertAdAndRender(cacheIndex, res)
          }
        } catch (e) {
          console.log('网络错误 error', e)
          this.loading = false
        }
      }
      this.showSetting = false
    } else {
      utils.showToast('看完了')
    }
  },

  openDrawer() {
    setTimeout(() => {
      this.showSetting = false
      // this.$element('drawer').openDrawer()
      this.$child('popup').show()
      this.$child('table-content').initData(this.currentPage.bookId)
    })
  },

  /**
   * 页面加载到时候会执行一次
   */
  handleChange({ index }) {
    // 华为手机下，此设置会造成死循环
    // this.swiperOpt.index = index
    this.swiperIndex = index
    if (!this.list[index]) return

    this.currentPage = this.list[index]
    const atLastPage = this.list.length - 1

    // 下一页
    if (index > this.prevIndex) {
      const chapterId = this.currentPage.nextChapterId
      if (
        chapterId &&
        index > atLastPage - this.prevThreshold &&
        !this.chapterList.find(it => it.chapterId === chapterId)
      ) {
        this.getData(chapterId, 'next')
      }
    }
    // 上一页
    else if (index < this.prevIndex) {
      const chapterId = this.currentPage.preChapterId
      if (
        chapterId &&
        index < this.prevThreshold &&
        !this.chapterList.find(it => it.chapterId === chapterId)
      ) {
        this.getData(chapterId, 'prev')
      }
    }

    this.prevIndex = index
  },

  handleAdLoad() {},

  handleAdClick() {
    this.adClicked = true
    this.swiperOpt.canSwipe = true
  },

  /**
   * 请求数据
   * @param chapterId {number}
   * @param type {'next'|'prev'}
   * @param loadDelay
   */
  getData(chapterId, type = 'next', loadDelay = 1500) {
    return new Promise((resolve, reject) => {
      console.log(
        `getData before getChapterContentData ${this.bookId} ${chapterId}`
      )
      getChapterContentData({ bookId: this.bookId, chapterId })
        .then(res => {
          if (!res) {
            console.log('getData reject', JSON.stringify(res))
            showToast('网络错误')
            return reject('网络错误')
          }

          console.log('getData before chapterList')
          if (!this.chapterList.find(it => it.chapterId === res.chapterId)) {
            if (type === 'next') {
              this.chapterList.push(res)
              if (this.chapterList.length > this.maxLen) {
                this.chapterList = this.chapterList.slice(-1 * this.maxLen)
              }
            } else {
              this.chapterList.unshift(res)
              if (this.chapterList.length > this.maxLen) {
                this.chapterList = this.chapterList.slice(0, this.maxLen)
              }
            }
          }

          console.log('getData before setTimeout')
          clearTimeout(this.timer)
          // 不加延时，再切换回来页面的时候会有卡顿
          this.timer = setTimeout(() => {
            console.log('getData before resolve')
            this.renderList()
            resolve(res)
            console.log('getData after resolve')
          }, loadDelay)
          console.log('getData after setTimeout')
        })
        .catch(e => {
          console.log(`getData reject ${JSON.stringify(e)}`)
        })
    })
  },

  delay(t = 0) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, t)
    })
  },

  // 这里必须有个默认值，不知道什么时候会有一个传一个undefine
  swiperToPage(index = 0) {
    console.log('swiperToPage index', index)
    this.currentPage = this.list[index]
    // this.swiperOpt.index = index
    this.swiperIndex = index
    this.$element('swiper').swipeTo({
      index: Math.min(index, this.list.length - 1),
    })
  },
  renderReader(chapterData) {
    const scale = 750 / this.$page.windowWidth
    // 翻页的容器高度
    const height =
      this.$page.windowHeight * scale - this.topHeight - this.bannerHeight
    const width = this.$page.windowWidth * scale - this.fontSize

    return loadReader({
      chapterContent: chapterData.content,
      chapterName: chapterData.chapterName,
      fontSize: this.currentFontSize,
      lineHeight: this.lineHeight,
      height,
      width,
    }).map(it => ({
      id: Math.random() * Math.random() * 10 + Date.now(),
      ...chapterData,
      ...it,
    }))
  },

  insertAdContainer(sourceList) {
    let listWithAd = []
    const intervalAdNum = this.intervalAdNum || 4

    sourceList.forEach((it, index) => {
      const adItem = {
        bookId: it.bookId,
        chapterId: it.chapterId,
        chapterName: it.chapterName,
        introduction: it.introduction,
        type: 'ad',
      }
      if (index % intervalAdNum) {
        listWithAd.push(it)
      } else {
        listWithAd.push(adItem, it)
      }
    })

    return listWithAd
  },

  handleClick(evt) {
    const windowWidth = 750
    const clientX = evt.clientX
    const efficientAreaWidth = windowWidth / 3

    if (clientX > efficientAreaWidth && clientX < efficientAreaWidth * 2) {
      this.showSetting = !this.showSetting
    } else {
      this.showSetting = false
    }
  },
}
</script>

<style lang="less">
.page {
  flex-direction: column;
  //padding-top: 100px;

  &__header {
    position: fixed;
    padding: 0 40px;
    //justify-content: space-between;
    width: 100%;
    //height: 100px;

    &__back-btn {
      margin-right: 20px;
      font-size: 24px;
      opacity: 0.5;
    }

    &__title {
      font-weight: bolder;
      font-size: 30px;
    }
  }
}

.swiper {
  flex-direction: column;
  background-color: #929292;
}

.swiper-item {
  flex-direction: column;
}

.swiper-item-ad {
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-header {
    flex-direction: column;
    width: 100%;
    align-items: center;
    padding: 40px 0;
  }

  &-title {
    font-size: 40px;
    font-weight: bold;
  }

  &-text {
    font-size: 28px;
    margin-top: 30px;
  }

  &-tip {
    font-size: 32px;
    margin: 15px 0;
  }
}

.banner-ad {
  justify-content: space-between;
  background-color: #fff;
}

.setting-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}

.spin-box {
  flex: 1;
}
</style>
