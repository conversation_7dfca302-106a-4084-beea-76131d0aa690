<template>
  <div class="wrapper" style="width: {{width}}">
    <image class="wrapper__cover" src="{{cover}}"></image>
    <div class="wrapper__info">
      <text class="wrapper__info__title">
        {{ title }}
      </text>
      <div class="wrapper__info__type">
        <slot name="type">
          <text>{{ category }}</text>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    cover: {
      default: '/assets/images/1.webp',
    },
    title: {
      default: '标题',
    },
    category: {
      default: '玄幻修真',
    },
    width: {
      default: '100%',
    },
  },
}
</script>

<style lang="less">
.wrapper {
  padding-right: 46px;
  box-sizing: border-box;

  &__cover {
    width: 100px;
    height: 136px;
    border-radius: 10px;
    margin-right: 20px;
  }

  &__info {
    flex: 1;
    flex-direction: column;
    justify-content: space-between;

    &__title {
      font-size: 28px;
      font-weight: bold;
      color: #333333;
      line-height: 42px;
      lines: 2;
      text-overflow: ellipsis;
    }

    &__type {
      text {
        font-size: 24px;
        color: rgba(41, 41, 56, 0.4);
      }
    }
  }
}
</style>
