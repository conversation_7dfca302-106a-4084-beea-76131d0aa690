import { trackEvent } from '@quickapp/business'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {
  protected: {
    bookId: 1,
    chapterId: 1,
    name: '开局买大力',
  },
  // onDestroy 中不能写异步回调
  onBackPress() {
    // this.$child('novel-reader').handleBackPress()
    return false
  },
  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'essay_show',
      opt_value: this.bookId,
    })
    console.log('page config onShow')
    this.$child('novel-reader').handlePageShow()
  },
}
export default setPageMenuConfig(pageConfig)
