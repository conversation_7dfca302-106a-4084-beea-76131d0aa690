<template>
  <div class="wrapper">
    <image class="logo" src="{{icon}}"></image>
    <text class="title" style="color: {{color}}">{{ appName }}</text>
    <div class="list">
      <slot></slot>
      <div class="list-item" @click="toPrivacyPage">
        <text class="text" style="color: {{color}}">隐私协议</text>
        <image class="icon" src="{{arrowRightIcon}}"></image>
      </div>
      <div class="list-item" @click="toUserPage">
        <text class="text" style="color: {{color}}">用户协议</text>
        <image class="icon" src="{{arrowRightIcon}}"></image>
      </div>
      <div class="list-item">
        <text class="text" style="color: {{color}}">
          版本：{{ versionName }}
          <span if='{{env === "development"}}'>_{{ env }}</span>
        </text>
      </div>
    </div>
  </div>
</template>

<script>
import { toPrivacyPage, toUserPage } from '@quickapp/utils'

export default {
  props: ['theme'],

  computed: {
    appName() {
      return __MANIFEST__.name
    },
    icon() {
      return __MANIFEST__.icon
    },
    versionName() {
      return __MANIFEST__.versionName
    },
    env() {
      return process.env.NODE_ENV
    },
    color() {
      return this.theme === 'dark' ? '#ffffff' : '#292938'
    },
    arrowRightIcon() {
      return this.theme === 'dark'
        ? 'http://cdn-inputmethod.springdance.cn/quickapp_assets/images/ic_arrow_right_white.webp'
        : 'http://cdn-inputmethod.springdance.cn/quickapp_assets/images/ic_arrow_right.webp'
    },
  },

  toPrivacyPage() {
    toPrivacyPage()
  },

  toUserPage() {
    toUserPage()
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding: 108px 30px 0;
}

.logo {
  width: 200px;
  height: 200px;
}

.title {
  font-size: 40px;
  font-weight: bold;
  /* color:#292938; */
  line-height: 40px;
  margin-top: 24px;
}

.list {
  flex-direction: column;
  width: 100%;
  margin-top: 180px;

  .list-item {
    justify-content: space-between;
    height: 100px;
    align-items: center;
    //border-bottom: 1px solid #eee;
  }

  .text {
    font-size: 32px;
    //color: #000000;
    /* color: #ffffff; */
  }

  .icon {
    width: 60px;
    height: 60px;
  }
}
</style>
