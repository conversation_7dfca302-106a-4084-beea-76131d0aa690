<import name="Mask" src="./mask.ux"></import>

<template>
  <div
    class="service-box"
    style="top: {{top}}px; left: {{left}}px; height: {{showService ? '158px' : '0px'}}"
  >
    <block if="{{showService}}">
      <image
        class="service-icon"
        @click="handleServerClick"
        src="http://fishing-h5.springdance.cn/images/service2.png"
      ></image>
      <image
        @click="closeService"
        class="service-close"
        if="{{false}}"
        src="http://fishing-h5.springdance.cn/images/close.webp"
      ></image>
      <!--<text style="color: {{textColor}}">帮助客服</text>-->
    </block>
    <Mask if="{{showMask}}">
      <div class="inner">
        <text class="title">我要举报</text>
        <text class="sub-title">
          请选择举报原因
          <span style="color: red">*</span>
        </text>
        <div class="list">
          <div class="list-item" for="item in list" @click="clickItem($idx)">
            <div
              class="list-item-ci  {{$idx === currentIndex ? 'list-item-ci--active' : ''}}"
            ></div>
            <text
              class='list-item-text {{$idx === currentIndex ? "list-item-text--active" : ""}}'
            >
              {{ item.text }}
            </text>
          </div>
        </div>
        <div class="exp">
          <text>情况说明</text>
          <input type="text" placeholder="请输入内容" />
        </div>
        <div class="exp">
          <text>联系方式</text>
          <input type="text" placeholder="请输入联系方式" />
        </div>
        <div class="footer">
          <text class="cancel" @click="handelCancel">取消</text>
          <text class="confirm" @click="handleConfirm">确定</text>
        </div>
      </div>
    </Mask>
    <Mask if="{{showMask2}}">
      <div class="final-inner">
        <text class="final-inner-title">抱歉给您带来不好的体验</text>
        <text class="final-inner-tip">
          我们将持续优化产品，并在{{ totalTime }}秒后帮您永久退
        </text>
        <div class="final-inner-footer">
          <text class="final-inner-footer-continue" @click="continueUse">
            继续使用
          </text>
          <text class="final-inner-footer-forever" @click="foreverExit">
            永久退出({{ totalTime }}s)
          </text>
        </div>
      </div>
    </Mask>
  </div>
</template>

<script>
// import { toServiceUrl2 } from "@quickapp/utils";
import router from '@system.router'
import { showToast, storage } from '@quickapp/utils'
import config from '@quickapp/business/lib/config'
import { trackEvent } from '@quickapp/business'

export default {
  props: {
    textColor: {
      type: String,
      default: '#333333',
    },
    positionTop: {
      type: String,
    },
    bottom: {
      type: Number,
      default: 320,
    },
    showService: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      top: 0,
      left: 0,
      currentIndex: -1,
      list: [
        { text: '广告展示太频繁，影响体验' },
        { text: '使用功能异常' },
        { text: '其他意见或建议' },
      ],
      showMask: false,
      showMask2: false,
      totalTime: 5,
      timer: null,
    }
  },

  onInit() {
    this.top = 1667 - 132 - this.bottom
    this.left = 0
  },

  handleServerClick() {
    this.showMask = true
  },
  toServicePage() {
    // toServiceUrl2();
    router.push({
      uri: 'pages/Service',
    })
  },

  clickItem(index) {
    this.currentIndex = index
  },

  handleConfirm() {
    if (this.currentIndex === -1) {
      showToast('请选择举报原因')
      return
    }
    setTimeout(() => {
      this.showMask = false
      this.showMask2 = true

      this.timer = setInterval(() => {
        if (this.totalTime <= 1) {
          clearInterval(this.timer)
          this.foreverExit()
          return
        }
        this.totalTime--
      }, 1000)
    })
  },

  handelCancel() {
    // 必须加 setTimeout，不加就不执行
    setTimeout(() => {
      this.showMask = false
    })
  },

  continueUse() {
    setTimeout(() => {
      this.showMask2 = false
      this.showMask = false
      clearInterval(this.timer)
      this.totalTime = 5
    })
  },

  async foreverExit() {
    setTimeout(() => {
      this.showMask2 = false
      this.showMask = false
      clearInterval(this.timer)
    })
    await storage.set('user-loathe', true)
    trackEvent({
      category: 'service',
      action: 'forever_exit',
    })
    setTimeout(() => {
      this.$app.exit()
    }, 500)
  },

  closeService() {},
}
</script>

<style lang="less">
.service-box {
  flex-direction: column;
  position: fixed;
  //left: 48px;
  //bottom: 150px;
  //top: 300px;
  font-weight: bolder;
  width: 152px;

  .service-close {
    width: 30px;
    height: 30px;
    margin-left: 110px;
    margin-top: -150px;
  }

  .service-icon {
    width: 152px;
    height: 70px;
  }
}

.dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: rgba(0, 0, 0, 0.7);
}

.inner {
  flex-direction: column;
  width: 600px;
  padding: 30px 40px;
  border-radius: 40px;
  background-color: white;

  .title {
    text-align: center;
    font-size: 36px;
    color: black;
    margin-top: 20px;
  }

  .sub-title {
    margin-top: 30px;
  }

  .list {
    flex-direction: column;
    margin-top: 40px;
    margin-bottom: 30px;

    &-item {
      margin-bottom: 20px;
      align-items: center;

      &-text {
        color: #333;

        &--active {
          color: red;
        }
      }

      &-ci {
        width: 30px;
        height: 30px;
        border-radius: 30px;
        background-color: #eee;
        margin-right: 30px;

        &--active {
          background-color: red;
        }
      }
    }
  }

  .exp {
    padding: 20px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    margin-top: -1px;

    input {
      font-size: 30px;
      margin-left: 20px;
    }
  }

  .footer {
    justify-content: space-between;
    margin-top: 30px;
    margin-bottom: 20px;

    text {
      width: 230px;
      height: 80px;
      font-size: 32px;
      text-align: center;
      border-radius: 8px;
    }

    .cancel {
      background-color: #eee;
    }
    .confirm {
      background-color: #ff5d5d;
      color: white;
    }
  }
}

.final-inner {
  flex-direction: column;
  align-items: center;
  width: 610px;
  height: 500px;
  padding: 0 26px;
  background-color: white;

  &-title {
    margin-top: 122px;
    font-size: 36px;
    font-weight: bold;
    color: #333333;
  }

  &-tip {
    width: 490px;
    text-align: center;
    font-size: 28px;
    color: #333333;
    line-height: 44px;
    margin-top: 46px;
  }

  &-footer {
    width: 100%;
    justify-content: space-between;
    margin-top: 54px;

    text {
      align-items: center;
      text-align: center;
      width: 268px;
      height: 96px;
      color: #ffffff;
      font-weight: 500;
      background-size: cover;
      background-repeat: no-repeat;
    }

    &-continue {
      background-color: #2d8cf0;
    }
    &-forever {
      background-color: #c8102e;
    }
  }
}
</style>
