<import name="my-rating" src="./rating"></import>

<template>
  <div class="wrapper">
    <list class="list" id="list">
      <list-item
        type="item"
        class="list__item {{item.type}}"
        for="item in list"
        tid="$idx"
      >
        <image class="list__item__avatar" src="{{item.avatar}}"></image>
        <div class="list__item__content">
          <text
            show='{{item.type === "service" && $idx > 0}}'
            class="list__item__content__name"
          >
            {{ item.name }}
          </text>
          <text
            @click="showDialog(item.isClick)"
            class="list__item__content__text {{item.isClick ? 'list__item__content__click' : ''}}"
          >
            {{ item.text }}
          </text>
        </div>
      </list-item>
    </list>
    <div class="input-box">
      <input
        class="input"
        value="{{userText}}"
        @change="updateUserTextValue"
        type="text"
      />
      <input class="btn" type="button" value="发送" @click="userSend" />
    </div>
    <div if="dialogVisible" class="dialog">
      <div class="dialog__container">
        <image
          @click="hideDialog"
          class="dialog__container__close"
          src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_close.png"
        ></image>
        <text class="dialog__container__title">是否屏蔽此快应用</text>
        <div class="dialog__container__footer">
          <text
            class="dialog__container__footer__btn shielding"
            @click="handleShielding"
          >
            残忍屏蔽
          </text>
          <text class="dialog__container__footer__btn use" @click="handleUse">
            继续使用
          </text>
        </div>
      </div>
    </div>

    <div if="dialogCommentVisible" class="dialog dialog-comment">
      <div class="dialog-comment__container">
        <div class="dialog-comment__container__header">
          <text class="dialog-comment__container__header__title">
            请对本次服务进行评价
          </text>
          <image
            @click="hideDialogComment"
            class="dialog-comment__container__header__close"
            src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_close.png"
          ></image>
        </div>
        <my-rating value="3"></my-rating>
        <!--model:value="comment"-->
        <textarea
          value="{{comment}}"
          @change="updateCommentValue"
          class="dialog-comment__container__textarea"
          placeholder="请输入您要反馈的意见"
          maxlength="200"
        ></textarea>
        <input
          @click="submit"
          class="dialog-comment__container__submit"
          type="button"
          value="提交评价"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { showToast } from '@quickapp/utils'
import config from '@quickapp/business/lib/config'

const serviceAvatar =
  'http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/custom_service_avatar.png'
const userAvatar =
  'http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_service_avatar.png'

export default {
  data() {
    return {
      list: [],
      userText: '',
      timer: null,
      dialogVisible: false,
      dialogCommentVisible: false,
      // 用户发送消息的最后时间
      userSendLastTime: Date.now(),
      // 会话结束时长
      sessionEndTime: 60 * 1000,
      interTimer: null,
      comment: '',
    }
  },

  onInit() {
    this.firstText()
    this.countDown()
  },

  async firstText() {
    await this.delay()
    this.pushServiceItem('您好，正在为你转接人工客服，请稍等~')

    await this.delay(1500)
    this.pushServiceItem('这边是在线客服，很高兴为您服务~')
  },

  userSend() {
    if (this.userText) {
      this.pushUserItem(this.userText)
      this.serviceSend(this.userText)
      this.userText = ''
    }
  },

  updateCommentValue(e) {
    this.comment = e.value
  },

  updateUserTextValue(e) {
    this.userText = e.value
  },

  serviceSend(userText = '') {
    if (!userText) return

    clearTimeout(this.timer)
    this.timer = setTimeout(async () => {
      if (userText.includes('广告')) {
        this.pushServiceItem(
          '感谢您的反馈与支持，这边会将您的反馈与建议提交给相关部门的~'
        )

        await this.delay()

        this.pushServiceItem(
          '亲亲~非常感谢您的热心反馈，您的反馈是督促我们前行的动力呢~ '
        )
      } else {
        this.pushServiceItem(
          '我们快应用是不会自己弹出的，只有点击我们投放的广告才会跳转到我们快应用中的哦~如果遇到自动跳转这种情况，可能是第三方劫持了我们的广告，针对您这种情，我们表示很抱歉。我们会先帮您屏蔽我们的广告~ '
        )
        await this.delay()
        this.pushServiceItem('点击此处屏蔽该应用广告', true)
      }
    }, 1000)
  },

  genUserText(text) {
    return {
      type: 'user',
      name: '您',
      avatar: userAvatar,
      text,
    }
  },

  genServiceText(text, isClick = false) {
    return {
      type: 'service',
      name: '酥饼大人',
      avatar: serviceAvatar,
      text,
      isClick,
    }
  },

  showDialog(isClick) {
    if (isClick) {
      this.dialogVisible = true
    }
  },
  hideDialog() {
    this.dialogVisible = false
  },

  handleShielding() {
    this.hideDialog()

    this.pushServiceItem(
      '亲亲~已帮您屏蔽此应用，如果再次发生这种情况辛苦您及时与我们联系！'
    )
  },

  handleUse() {
    this.hideDialog()
    this.pushServiceItem(
      '亲亲~非常感谢您的热心反馈，您的反馈是督促我们前行的动力呢~'
    )
  },

  pushList(item) {
    this.list.push(item)
    const listEle = this.$element('list')
    listEle &&
      listEle.scrollTo({
        index: this.list.length - 1,
      })
  },

  pushServiceItem(text, isClick = false) {
    this.pushList(this.genServiceText(text, isClick))
  },

  pushUserItem(text, isClick = false) {
    this.pushList(this.genUserText(text, isClick))
    this.userSendLastTime = Date.now()
  },

  delay(t = 1000) {
    return new Promise(resolve => {
      setTimeout(resolve, t)
    })
  },

  // 会话是否结束
  isSessionEnd() {
    return Date.now() - this.userSendLastTime >= this.sessionEndTime
  },

  countDown() {
    clearInterval(this.interTimer)

    this.interTimer = setInterval(async () => {
      if (this.isSessionEnd()) {
        clearInterval(this.interTimer)
        this.pushUserItem(
          '由于您长时间未回复，系统将自动关闭会话。如您有需要可随时再联系我们，客服24小时在线。祝您生活愉快，天天开心。'
        )
        await this.delay(500)
        this.showDialogComment()
      }
    }, 5 * 1000)
  },

  showDialogComment() {
    this.dialogCommentVisible = true
  },

  hideDialogComment() {
    this.dialogCommentVisible = false
  },

  submit() {
    if (!this.comment) {
      return showToast('请输入评价')
    }

    this.hideDialogComment()
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.list {
  flex: 1;
  padding: 30px;
  background-color: #eeeeee;

  .service {
    .list__item__avatar {
      margin-right: 16px;
    }
  }

  .user {
    flex-direction: row-reverse;

    .list__item__avatar {
      margin-left: 16px;
    }

    .list__item__content__name {
      text-align: right;
    }

    .list__item__content__text {
      color: white;
      background-color: #ed5b3b;
    }
  }

  &__item {
    margin-top: 40px;

    &__avatar {
      width: 72px;
      height: 72px;
      border-radius: 50%;
    }

    &__content {
      flex-direction: column;

      &__name {
        font-size: 24px;
        margin-bottom: 10px;
      }

      &__text {
        width: 528px;
        padding: 20px 24px;
        line-height: 45px;
        font-size: 30px;
        border-radius: 20px;
        color: #333;
        background-color: white;
      }

      &__click {
        text-decoration: underline;
      }
    }
  }
}

.input-box {
  background-color: #dfdfdf;
  padding: 20px;
  height: 176px;

  input {
    height: 96px;
    border-radius: 8px;
  }

  .input {
    flex: 1;
    background-color: white;
  }

  .btn {
    width: 138px;
    height: 96px;
    font-size: 32px;
    color: #ffffff;
    background-color: #ed5b3b;
    margin-left: 8px;
  }
}

.dialog {
  position: fixed;
  left: 0;
  top: 0;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);

  &__container {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 610px;
    padding: 80px 40px 40px;
    background-color: #ffffff;
    border-radius: 20px;

    &__close {
      position: absolute;
      right: 30px;
      top: 30px;
    }

    &__title {
      font-size: 36px;
      font-weight: bolder;
      color: #333333;
      line-height: 36px;
    }

    &__footer {
      width: 100%;
      justify-content: space-between;
      margin-top: 60px;

      &__btn {
        width: 260px;
        height: 88px;
        text-align: center;
        font-size: 32px;
        border-radius: 16px;
        font-weight: bolder;
      }

      .shielding {
        color: #96876b;
        border: 2px solid #96876b;
      }

      .use {
        color: white;
        background-color: #ed5b3b;
      }
    }
  }
}

.dialog-comment {
  align-items: flex-end;
  &__container {
    flex-direction: column;
    align-items: center;
    width: 100%;
    background-color: white;
    padding-bottom: 40px;

    &__header {
      width: 100%;
      justify-content: space-between;
      border-bottom: 1px solid #eee;
      padding: 20px 40px;
      margin-bottom: 30px;

      &__title {
        font-size: 36px;
        font-weight: bolder;
      }

      &__close {
        width: 50px;
      }
    }

    &__textarea {
      width: 690px;
      height: 300px;
      margin: 30px;
      padding: 20px;
      font-size: 30px;
      border: 1px solid #eee;
      border-radius: 8px;
    }

    &__submit {
      width: 690px;
      height: 80px;
      border-radius: 8px;
      color: white;
      background-color: #ed5b3b;
    }
  }
}
</style>
