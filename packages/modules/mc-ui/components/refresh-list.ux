<!--
  快应用不支持作用域插槽, 这个组件没什么吊用
-->
<import name="my-spin" src="@quickapp/apex-ui/components/spin/index"></import>

<template>
  <div class="wrapper">
    <refresh
      class="refresh"
      offset="132px"
      refreshing="{{pulldownrefreshing}}"
      @refresh="handlePulldownrefresh"
    >
      <my-spin tip="加载中..." nested="{{true}}" loading="{{firstLoading}}">
        <list slot="nested" class="list" @scrollbottom="handlePulluprefresh">
          <list-item class="item" type="listItem" for="{{dataList}}" tid="$idx">
            <slot></slot>
          </list-item>
          <list-item class="loading-box" type="loading">
            <text if="{{pulluprefreshing}}">正在加载...</text>
            <text elif="{{!loadMoreEnabled}}">------ 我是有底线的 ------</text>
          </list-item>
        </list>
      </my-spin>
    </refresh>
  </div>
</template>

<script>
export default {
  props: {
    dataApi: {
      type: Function,
      default: () =>
        Promise.resolve({
          records: [1, 2],
          pages: 2,
        }),
    },
  },

  data() {
    return {
      pulluprefreshing: false,
      pulluprefresh: false,
      pulldownrefreshing: false,
      dataList: [],
      // 总页数
      currentTotalPages: Number.MAX_SAFE_INTEGER,
      // 当前页
      currentPage: 1,
      pageSize: 10,
      firstLoading: false,
    }
  },

  computed: {
    loadMoreEnabled: {
      get() {
        return this.currentPage < this.currentTotalPages
      },
    },
  },

  onInit() {
    this.firstLoading = true

    this.getData().finally(() => {
      this.firstLoading = false
    })
  },

  getData() {
    return this.dataApi({
      pageNum: this.currentPage,
      size: this.pageSize,
    }).then(res => {
      if (res) {
        console.log('res', res)
        const results = res.records

        if (this.currentPage > 1) {
          results.forEach(it => {
            this.dataList.push(it)
          })
        } else {
          this.dataList = results
        }
        this.currentTotalPages = res.pages
      }
    })
  },

  handlePulluprefresh() {
    console.log('上拉')
    if (this.loadMoreEnabled) {
      this.pulluprefreshing = true
      this.currentPage += 1
      this.getData().finally(() => {
        this.pulluprefreshing = false
      })
    } else {
      this.pulluprefreshing = false
    }
  },
  handlePulldownrefresh() {
    console.log('下拉')
    this.pulldownrefreshing = true
    this.currentPage = 1
    this.getData().finally(() => {
      this.pulldownrefreshing = false
    })
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  height: 100%;
  .list {
    flex: 1;
    padding: 20px 0;
    background-color: #ffffff;
  }
}

.refresh {
  flex: 1;
}

.loading-box {
  height: 130px;
  width: 100%;
  justify-content: center;
  align-items: center;
  /* background-color: #faebd7; */
  .loading {
    color: rgba(0, 0, 0, 0.3);
    /* font-size: 22px; */
  }
}
</style>
