<import name="dialog-protocol" src="./dialog-protocol.ux"></import>

<template>
  <div class="wrapper">
    <dialog-protocol
      @to-privacy="handleToPrivacyEvt"
      @to-user="handleToUserEvt"
      @close="handleClose"
      auto-click="{{autoClick}}"
    ></dialog-protocol>
  </div>
</template>

<script>
import {
  registerDevice,
  storage,
  toPrivacyPage,
  toUserPage,
} from '@quickapp/utils'
import router from '@system.router'
import { register } from '@quickapp/business'
import { API_HOST } from '@quickapp/utils/lib/request'
import globalData from '@quickapp/utils/lib/globalData'

export default {
  data: {},

  props: {
    clickId: {
      type: String,
    },
    autoAgree: {
      type: String,
    },
    browserInfo: {
      type: String,
    },
    // 小说id 66
    bookId: {
      type: Number,
    },
    // 章节id 45275
    chapterId: {
      type: Number,
    },
    appType: {
      type: String,
    },
    clickType: {
      type: String,
    },
    webviewPage: {
      default: 'pages/Web',
    },
    privacyPageParams: {
      default: {},
    },
    userPageParams: {
      default: {},
    },
    mainPage: {
      default: 'pages/Home',
    },
  },
  computed: {
    autoClick() {
      return this.autoAgree === '1'
    },
  },
  handleToPrivacyEvt() {
    toPrivacyPage(this.webviewPage, this.privacyPageParams)
  },
  handleToUserEvt() {
    toUserPage(this.webviewPage, this.userPageParams)
  },
  async handleClose() {
    await this.setDisabledMenu()
    await register(this.clickId)
    await this.registerDeviceRequest()
    await this.toMainPageAndTrackEvent()
  },
  registerDeviceRequest() {
    const type = this.appType || 'novel'
    return registerDevice(API_HOST + `/${type}/user/register_device`)
  },
  async setDisabledMenu() {
    if (this.autoClick) {
      await storage.set('autoClick', '1')
    }
    const isAuto = await storage.get('autoClick')
    // 是否禁用菜单
    globalData.isDisabledMenu = isAuto === '1'
  },
  toMainPageAndTrackEvent() {
    const params = {}
    if (this.bookId) {
      params.bookId = this.bookId
    }
    if (this.chapterId) {
      params.chapterId = this.chapterId
    }
    router.replace({
      uri: this.mainPage,
      params: {
        ...params,
        ___PARAM_PAGE_ANIMATION___: {
          openEnter: `none`,
          closeEnter: `slide`,
          openExit: `slide`,
          closeExit: `slide`,
        },
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: flex-end;
  background-image: url('/assets/images/flash_bg.webp');
  background-size: cover;
}
</style>
