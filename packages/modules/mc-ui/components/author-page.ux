<import name="nav-header" src="./nav-header.ux"></import>
<import name="img-text" src="./layout/img-text.ux"></import>
<import name="novel-card" src="./novel/novel-card.ux"></import>
<import name="spannable-text" src="./spannable-text.ux"></import>

<template>
  <div class="page">
    <nav-header></nav-header>
    <div style="padding: 20px 40px 40px; border-bottom: 20px solid #f7f7f7">
      <img-text
        title="酥饼"
        sub-title="作品《封神帮》"
        img-url="/assets/images/1.webp"
      ></img-text>
    </div>
    <list class="list">
      <list-item type="item" for="item in 10">
        <novel-card>
          <spannable-text
            slot="other"
            content="狂奔的蜗牛"
            span-content="蜗牛"
            text-style="font-size: 24px; color: #292938;"
          ></spannable-text>
        </novel-card>
      </list-item>
      <list-item type="placeholder" style="height: 60px"></list-item>
    </list>
  </div>
</template>

<script>
import SpannableText from './spannable-text.ux'

export default {
  components: { SpannableText },
}
</script>

<style lang="less">
.page {
  padding-top: 88px;
  flex-direction: column;
  height: 100%;
}

.list {
  padding: 0 40px;
}
</style>
