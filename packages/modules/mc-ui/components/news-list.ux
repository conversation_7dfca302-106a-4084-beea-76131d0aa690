<!--
  快应用不支持作用域插槽, 这个组件没什么吊用
-->
<import name="my-spin" src="@quickapp/apex-ui/components/spin/index"></import>

<template>
  <div class="wrapper">
    <refresh
      class="refresh"
      offset="132px"
      refreshing="{{pulldownrefreshing}}"
      @refresh="handlePulldownrefresh"
    >
      <my-spin tip="加载中..." nested="{{true}}" loading="{{firstLoading}}">
        <list slot="nested" class="list" @scrollbottom="handlePulluprefresh">
          <list-item
            class="item"
            type="listItem"
            for="{{item in dataList}}"
            tid="{{$idx}}"
            @click="handleClick(item)"
          >
            <div class="item__top">
              <text class="item__top__title">{{ item.title }}</text>
              <image
                if="{{item.img}}"
                class="item__top__img"
                src="{{item.img}}"
              ></image>
            </div>
            <div class="item__footer">
              <text class="item__footer__date">{{ item.date }}</text>
              <text if="{{!item.img}}" class="item__footer__author-name">
                {{ item.authorName }}
              </text>
            </div>
          </list-item>
          <list-item class="loading-box" type="loading">
            <text if="{{pulluprefreshing}}">正在加载...</text>
            <text elif="{{!loadMoreEnabled}}">------ 我是有底线的 ------</text>
          </list-item>
        </list>
      </my-spin>
    </refresh>
  </div>
</template>

<script>
import { createRequest } from '@quickapp/utils'
import router from '@system.router'
const DEBUG = process.env.NODE_ENV !== 'production'
const fat = {
  API_HOST: 'http://quick-app-api-fat.springdance.cn/quick-app',
}
const pro = {
  API_HOST: 'http://quick-app-api.springdance.cn/quick-app',
}
const request = createRequest(DEBUG ? fat.API_HOST : pro.API_HOST, false)

const defaultApi = ({ pageNum, size }) =>
  request({
    url: '/h5/news',
    data: {
      pageNum,
      size,
    },
  })

export default {
  props: {
    dataApi: {
      type: Function,
      default: defaultApi,
    },
  },

  data: {
    pulluprefreshing: false,
    pulluprefresh: false,
    pulldownrefreshing: false,
    dataList: [],
    // 总页数
    currentTotalPages: Number.MAX_SAFE_INTEGER,
    // 当前页
    currentPage: 1,
    pageSize: 10,
    firstLoading: false,
  },

  computed: {
    loadMoreEnabled: {
      get() {
        return this.currentPage < this.currentTotalPages
      },
    },
  },

  onInit() {
    this.firstLoading = true

    setTimeout(() => {
      this.getData().finally(() => {
        this.firstLoading = false
      })
    }, 300)
  },

  getData() {
    const request = this.dataApi

    return request({
      pageNum: this.currentPage,
      size: this.pageSize,
    }).then(res => {
      if (res) {
        console.log('res 新闻', JSON.stringify(res))
        const results = res.records

        if (this.currentPage > 1) {
          results.forEach(it => {
            this.dataList.push(it)
          })
        } else {
          this.dataList = results
        }
        this.currentTotalPages = res.pages
      }
    })
  },

  handlePulluprefresh() {
    console.log('上拉')
    if (this.loadMoreEnabled) {
      this.pulluprefreshing = true
      this.currentPage += 1
      this.getData().finally(() => {
        this.pulluprefreshing = false
      })
    } else {
      this.pulluprefreshing = false
    }
  },
  handlePulldownrefresh() {
    console.log('下拉')
    this.pulldownrefreshing = true
    this.currentPage = 1
    this.getData().finally(() => {
      this.pulldownrefreshing = false
    })
  },

  handleClick(item) {
    router.push({
      uri: 'pages/Web',
      params: {
        webSrc: item.url,
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  height: 100%;
  .list {
    flex: 1;
    padding: 20px;
    background-color: #ffffff;

    .item {
      flex-direction: column;
      padding: 20px 0;
      border-bottom: 1px solid #eeeeee;

      &__top {
        justify-content: space-between;
        align-items: flex-start;

        &__title {
          flex: 1;
          color: black;
          line-height: 44px;
        }

        &__img {
          width: 220px;
          height: 120px;
          margin-left: 20px;
          object-fit: cover;
          resize-mode: cover;
        }
      }

      &__footer {
        margin-top: 20px;
        justify-content: space-between;

        &__date,
        &__author-name {
          font-size: 24px;
          line-height: 36px;
          color: rgba(0, 0, 0, 0.4);
        }
      }
    }
  }
}

.refresh {
  flex: 1;
}

.loading-box {
  height: 130px;
  width: 100%;
  justify-content: center;
  align-items: center;
  .loading {
    color: rgba(0, 0, 0, 0.3);
  }
}
</style>
