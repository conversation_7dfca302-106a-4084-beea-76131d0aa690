<template>
  <div  style='width: {{showShortcutBtn ? width : "0px"}}'>
    <block if="{{showShortcutBtn}}">
      <input if="{{isOPPO}}" type="{{eventButton}}"  eventtype="{{eventType}}" class="add-desk"  value="{{text}}" @click='handleOppo' style='{{btnStyle}}' />
      <input
        else
        type="button"
        class="add-desk"
        value="{{text}}"
        @click="addDesk"
        style='{{btnStyle}}'
      ></input>
    </block>
  </div>
</template>


<script>
import shortcut from '@system.shortcut'
import { showToast, storage } from '@quickapp/utils'
import { showDialog } from '@system.prompt'

export default {
  props: {
    text: {
      default: '无需下载，添加至桌面'
    },
    width: {
      default: '400px'
    },
    height: {
      default: '80px'
    },
    bgColor: {
      default: '#7490ff'
    },
    fontSize: {
      default: '32px'
    },
    borderRadius: {
      default: "44px"
    }
  },

  data() {
    return {
      eventButton:"eventbutton",
      eventType:"shortcut",
      isOPPO:false,
      showShortcutBtn: true
    }
  },

  computed: {
    btnStyle() {
      return `width: ${this.width}; height: ${this.height}; background-color: ${this.bgColor}; font-size: ${this.fontSize}; border-radius: ${this.borderRadius}`
    }
  },

  onInit() {
    shortcut.hasInstalled({
      success: result => {
        console.log('result', result)
        this.showShortcutBtn = !result
      },
    })

    storage.get("deviceInfo").then(res => {
      console.log('res', res)
      this.isOPPO = res && res.brand &&  res.brand.toLowerCase() === 'oppo'
      console.log('this.this.isOPPO', this.isOPPO)
    })
  },

  addDesk() {
    shortcut.install({
      success: () => {
        this.showShortcutBtn = false
        this.$emit("success")
      },
      fail: (data, code) => {
        console.log('添加桌面失败 ', JSON.stringify(data), code)
        this.$emit("fail", { data,code })
        if (code === 201) {
          showToast("添加桌面失败,您拒绝添加到桌面了")
        } else if (code === 207) {
          showToast("添加桌面失败,您拒绝并选择不再询问")
        } else {
          showToast("添加桌面失败,今日添加桌面次数过多")
        }
        this.showShortcutBtn = true
      }
    })
  },

  handleOppo(evt) {
    const { event_status_code = '空' } = evt;
    this.showShortcutBtn = event_status_code != 0
    if (event_status_code == 0) {
      this.$emit("success")
    }
  }
}

</script>

<style>

.add-desk {
  /*width: 400px;*/
  /*height: 80px;*/
  /*background-color: #7490ff;*/
  /* border-radius: 44px; */
  /*font-size: 32px;*/
  font-weight: bolder;
  color: #ffffff;
}
</style>