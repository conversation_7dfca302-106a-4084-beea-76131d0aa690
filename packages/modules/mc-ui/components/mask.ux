<template>
  <div class="wrapper" style="{{wrapperStyle}}">
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    alignItems: {
      default: 'center',
    },
    justifyContent: {
      default: 'center',
    },
  },
  computed: {
    wrapperStyle() {
      return `align-items: ${this.alignItems}; justify-content: ${this.justifyContent}`
    },
  },
}
</script>

<style lang="less">
.wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: rgba(0, 0, 0, 0.7);
}
</style>
