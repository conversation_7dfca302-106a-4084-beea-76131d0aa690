<template>
  <div class="wrapper">
    <div class="wrapper__title">
      <slot name="title">
        <text
          if="{{title}}"
          class="wrapper__title__text"
          @click="handleClickTitle"
        >
          {{ title }}
        </text>
      </slot>
    </div>
    <div class="wrapper__back">
      <slot name="left">
        <image
          class="wrapper__back__icon"
          src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_back2.webp"
          @click="back"
        ></image>
      </slot>
    </div>
    <div class="wrapper__edit">
      <slot name="right">
        <text if="{{editText}}" class="wrapper__edit__text" @click="handleEdit">
          {{ editText }}
        </text>
      </slot>
    </div>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  props: {
    title: {
      default: '',
    },

    editText: {
      default: '',
    },
  },

  back() {
    router.back()
  },

  handleEdit() {
    this.$emit('edit')
  },

  handleClickTitle() {
    this.$emit('clickTitle')
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 88px;
  align-items: center;
  padding: 0 20px;

  &__back {
    &__icon {
      width: 56px;
      height: 56px;
    }
  }

  &__title {
    width: 100%;
    position: absolute;
    text-align: center;
    justify-content: center;

    &__text {
      font-size: 32px;
      font-weight: bold;
      color: #333333;
      line-height: 32px;
    }
  }

  &__edit {
    margin-left: auto;

    &__text {
      font-size: 28px;
      font-weight: bold;
      color: #999999;
      line-height: 32px;
    }
  }
}
</style>
