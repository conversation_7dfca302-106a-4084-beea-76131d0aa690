<template>
  <div class="rating">
    <div for="{{star}}" onclick="handleClick($idx)">
      <image
        if="currentValue > $idx - 1"
        class="ic-star"
        src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_star_active.png"
      ></image>
      <image
        else
        class="ic-star"
        src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_star.png"
      ></image>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    count: {
      default: 5,
    },
    margin: {
      default: 4,
    },
    value: {
      default: 0,
    },
    disabled: {
      default: false,
    },
  },
  data() {
    return {
      star: [],
      currentValue: this.value - 1,
    }
  },
  onInit() {
    this.star = Array.from({ length: this.count }, () => '')
  },
  handleClick(i) {
    if (this.disabled) {
      return false
    }
    if (this.currentValue === i) {
      this.currentValue = i - 1
    } else {
      this.currentValue = i
    }
    this.$emit('change', i + 1)
  },
}
</script>

<style lang="less">
.rating {
  flex-direction: row;
  justify-content: center;
}

.ic-star {
  width: 60px;
  height: 60px;
  margin: 0 10px;
}
</style>
