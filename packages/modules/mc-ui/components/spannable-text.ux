<template>
  <text class="wrapper" style="height: {{height}}; width: {{width}}">
    <!--span不支持事件-->
    <a
      for="item in textList"
      tid="$idx"
      style="{{textStyle}}; {{$idx === 1 ? spanStyle : ''}}"
      @click="handleClickSpan($idx, item)"
    >
      {{ item }}
    </a>
  </text>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
      required: true, // 必填项检查
      // default: '详细解析',
    },
    // 要润色的子内容，如果存在 spanContent ，那么就不读取 start 和 end
    spanContent: {
      default: '',
    },
    // 需要润色文字段开始的下标；
    start: {
      default: 0,
    },
    // 需要润色文字段结束的下标；
    end: {
      default: Number.MAX_VALUE,
    },
    // 暂时只支持 SPAN_INCLUSIVE_EXCLUSIVE: 包括开始下标，但不包括结束下标
    flags: {
      // SPAN_INCLUSIVE_EXCLUSIVE：包括开始下标，但不包括结束下标
      // SPAN_EXCLUSIVE_INCLUSIVE：不包括开始下标，但包括结束下标
      // SPAN_INCLUSIVE_INCLUSIVE：既包括开始下标，又包括结束下标
      // SPAN_EXCLUSIVE_EXCLUSIVE：不包括开始下标，也不包括结束下标
      default: 'SPAN_INCLUSIVE_EXCLUSIVE',
    },
    // 润色样式
    spanStyle: {
      default: 'color: #F65555',
    },
    // 基础样式
    textStyle: {
      default: '',
    },
    height: {
      default: 'auto',
    },
    width: {
      default: 'auto',
    },
  },
  computed: {
    textList() {
      let start = this.start
      let end = this.end

      if (this.spanContent) {
        const index = this.content.indexOf(this.spanContent)
        if (index !== -1) {
          start = index
          end = index + this.spanContent.length
        }
      }

      const str1 = this.content.substring(0, start)
      const activeStr = this.content.substring(start, end)
      const str3 = this.content.substring(end)

      return [str1, activeStr, str3]
    },
  },

  handleClickSpan(index, text, $evt) {
    if (index === 1) {
      this.$emit('clickSpan', { text, $evt })
    }
  },
}
</script>
