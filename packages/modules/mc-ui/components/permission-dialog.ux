<script>
export default {
  data: {
    showDialog: false,
  },
  props: {
    permissionText: {
      type: String,
      default:
        '权限使用说明: 用于拍照/选取图片进行文字识别或文字识别后保存读取、复制等操作',
    },
  },
  show() {
    this.showDialog = true
  },
  hide() {
    this.showDialog = false
  },
}
</script>

<template>
  <div class="dialog-wrapper" show="{{showDialog}}">
    <div class="dialog">
      <text>{{ permissionText }}</text>
    </div>
  </div>
</template>

<style lang="less">
.dialog-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 99;

  .dialog {
    width: 678px;
    height: 200px;
    margin-top: 50px;
    border-radius: 40px;
    background-color: white;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text {
      color: #333;
      font-size: 36px;
    }
  }
}
</style>
