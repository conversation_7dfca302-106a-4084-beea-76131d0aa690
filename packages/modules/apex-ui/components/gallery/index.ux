<import name="my-icon" src="../icon/index"></import>

<template>
  <div class="{{isShow ? 'apex-gallery':''}}">
    <swiper
      class="apex-gallery-swiper"
      style="{{customStyle}}"
      indicator="{{indicator}}"
      autoplay="{{autoplay}}"
      interval="{{ interval }}"
      duration="{{ duration }}"
      loop="{{ loop }}"
      vertical="{{ vertical }}"
      index="{{ index }}"
      if="{{ isShow && srcs.length }}"
      onchange="handlerChange"
    >
      <div class="apex-gallery-img-wrap" for="{{(idx, image) in srcs}}">
        <image
          class="apex-gallery-img"
          src="{{image}}"
          onclick="onClickSwiperImage"
        ></image>
      </div>
    </swiper>
  </div>
</template>

<script>
const defaultData = {
  isShow: false,
  indicator: true,
  indicatorColor: "rgba(0, 0, 0, .5)",
  indicatorSelectedColor: "#33b4ff",
  autoplay: false,
  interval: 2000,
  duration: 500,
  loop: true,
  vertical: false,
  index: 0,
  srcs: []
};

export default {
  data() {
    return {
      ...defaultData
    };
  },

  computed: {
    customStyle() {
      return `indicator-color:${this.indicatorColor};indicator-selected-color:${this.indicatorSelectedColor};`;
    }
  },

  showGallery(options) {
    Object.assign(this, {
      ...options,
      isShow: true
    });
  },

  hideGallery() {
    Object.assign(this, {
      ...defaultData
    });
  },

  onClickSwiperImage() {
    this.hideGallery();
  },

  handlerChange(e) {
    this.index = e.index;
    this.$emit("change", {
      index: e.index
    });
  }
};
</script>

<style lang="less">
@import "../styles/base.less";

.apex-gallery {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: #eee;

  &-swiper {
    width: 100%;
    height: 100%;
    indicator-size: 20px * @ratio;
  }

  &-img-wrap {
    width: 100%;
    height: 100%;
    flex-direction: column;
    justify-content: center;
  }

  &-img {
    object-fit: contain;
  }
}
</style>
