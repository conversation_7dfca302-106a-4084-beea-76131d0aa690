<template>
  <text
    class="font-icon"
    style="font-size: {{size*ratio/750}}px;color: {{color}};"
    >{{ unescapeFontIconCode(iconMap[type]) }}
  </text>
</template>
<style lang="less">
@font-face {
  font-family: iconfont;
  src: url("./iconfonts.ttf");
}

.font-icon {
  font-family: iconfont;
  text-align: center;
}
</style>
<script>
import { icons } from "./icons";

export default {
  data() {
    return {
      iconMap: icons
    };
  },
  props: {
    type: {
      default: "empty"
    },
    size: {
      default: 14
    },
    color: {
      default: ""
    },
    ratio: {
      default: 750
    }
  },
  unescapeFontIconCode(iconCode = "") {
    return unescape(iconCode.replace(/&#x/g, "%u").replace(/;/g, ""));
  }
};
</script>
