<template>
  <stack>
    <div if="{{nested}}">
      <slot name="nested"></slot>
    </div>
    <div
      id="apex-spin"
      if="{{isLoading}}"
      style="{{nested ? 'background-color: rgba(255, 255, 255, 0.75);' : ''}}"
    >
      <div if="{{!custom}}" class="apex-spin">
        <progress
          class="apex-spin-{{size}}"
          type="circular"
          style="color:{{color}};"
        ></progress>
        <text class="tip tip-{{size}}" if="{{tip}}" style="color:{{color}};">{{
          tip
        }}</text>
      </div>
      <div else>
        <slot name="custom"></slot>
      </div>
    </div>
  </stack>
</template>

<style lang="less">
@import "../styles/base.less";

.apex-spin {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  &-normal {
    width: 80px * @ratio;
    height: 80px * @ratio;
  }
  &-small {
    width: 50px * @ratio;
    height: 50px * @ratio;
  }
  &-large {
    width: 100px * @ratio;
    height: 100px * @ratio;
  }
}

.tip {
  lines: 1;
  text-overflow: ellipsis;
  &-normal {
    font-size: 40px * @ratio;
    margin-top: 10px * @ratio;
  }
  &-small {
    font-size: 30px * @ratio;
    margin-top: 8px * @ratio;
  }
  &-large {
    font-size: 45px * @ratio;
    margin-top: 15px * @ratio;
  }
}
</style>

<script>
export default {
  data() {
    return {
      custom: false,
      isLoading: this.loading
    };
  },

  props: {
    size: {
      type: String,
      default: "normal"
    },
    tip: {
      type: String,
      default: ""
    },
    color: {
      type: String,
      default: "#33b4ff"
    },
    nested: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: true
    },
    custom: {
      type: Boolean,
      default: false
    }
  },

  onInit() {
    this.$watch("loading", "watchLoadingChange");
  },

  watchLoadingChange(newV, oldV) {
    this.isLoading = newV;
  }
};
</script>
