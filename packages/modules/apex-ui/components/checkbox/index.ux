<template>
  <div class="apex-checkbox apex-checkbox-{{position}}" onclick="clickHandler">
    <div
      class="apex-checkbox-checked {{my_checked?'apex-checkbox-active':''}} apex-checkbox-{{shape}}"
      style="background-color: {{(my_checked||disabled)?my_color:''}}"
    >
      <div
        class="apex-checkbox-icon {{disabled?'apex-checkbox-disabled':''}}"
        show="{{my_checked}}"
      ></div>
    </div>
    <div class="apex-checkbox-content">
      <text>{{ value }}</text>
    </div>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

.apex-checkbox {
  padding: 20px * @ratio;
  background-color: #ffffff;
  height: 86px * @ratio;

  &-checked {
    height: @checkbox-size;
    width: @checkbox-size;
    justify-content: center;
    border-radius: @checkbox-size;
    border: 2px * @ratio solid @border-color-base;
    margin-right: 10px * @ratio;
  }

  &-icon {
    width: @checkbox-size / 2;
    height: @checkbox-size / 3.6;
    border-style: solid;
    border-width: 0 0 2px * @ratio 2px * @ratio;
    transform: rotate(-45deg);
    margin-top: @checkbox-size / 3;
    border-color: #ffffff;
  }

  &-active {
    border-color: transparent;
  }

  &-disabled {
    border-color: @border-color-base;
  }

  &-right {
    flex-direction: row-reverse;
    justify-content: space-between;
  }

  &-square {
    border-radius: 5px * @ratio;
  }

  text {
    font-size: @text-size;
  }
}
</style>

<script>
export default {
  data() {
    return {
      my_checked: this.checked,
      my_position: this.position,
      my_color: this.color
    };
  },
  props: {
    checked: {
      default: false
    },
    disabled: {
      default: false
    },
    shape: {
      default: "circle" // circle square
    },
    position: {
      default: "left" // left right
    },
    value: {
      default: ""
    },
    color: {
      default: "#2d8cf0"
    },
    group: {
      default: ""
    }
  },
  onInit() {
    this.$watch("disabled", "handleDisabled");
    this.$watch("checked", "changeChecked");
    if (this.group) {
      const parent = this.$parent().$child(this.group);
      const temp = parent.checkboxes;
      const checkboxes = temp ? [...temp, this] : [this];
      parent.checkboxes = checkboxes;
    }
  },
  changeChecked() {
    this.my_checked = this.checked;
  },
  changeCurrent(current) {
    this.my_checked = current;
  },
  clickHandler() {
    if (this.disabled) return;
    const item = { current: !this.checked, value: this.value };
    const parent = this.$parent().$child(this.group);
    parent ? parent.emitEvent(item) : this.$emit("change", item);
  },
  handleDisabled(e) {
    if (e) {
      this.my_color = "#bbbec4";
    } else {
      this.my_color = this.color;
    }
  }
};
</script>
