<template>
  <div
    class="apex-input {{ error ? 'apex-input-error' : '' }} {{ mode === 'wrapped' ? 'apex-input-wrapped' : '' }}"
  >
    <text class="apex-input-title title" if="{{!!title}}">{{ title }}</text>
    <input
      id="input"
      class="apex-input-content {{right === 'true' ? 'apex-input-right': ''}}"
      type="{{type}}"
      maxlength="{{maxlength}}"
      placeholder="{{placeholder}}"
      value="{{my_value}}"
      if="{{disabled !== 'true'}}"
      onchange="handleInputChange"
      onfocus="handleInputFocus"
    />
    <text
      class="apex-input-content {{right === 'true' ? 'apex-input-right': ''}}"
      if="{{disabled === 'true'}}"
      >{{ my_value }}</text
    >
  </div>
</template>
<style lang="less">
@import "../styles/base";

.apex-input {
  flex-direction: row;
  background-color: #ffffff;
  padding: 20px * @ratio;
  &-title {
    font-size: @size-font-base;
    margin-right: 20px * @ratio;
  }
  &-content {
    flex-grow: 1;
    font-size: @size-font-base;
  }
  &-wrapped {
    margin: 10px * @ratio 15px * @ratio;
    background-color: #fff;
    border: 1px solid @border-color-base;
    border-radius: 8px * @ratio;
  }
  &-right {
    text-align: right;
  }
  &-error {
    border: 1px solid @error-color;
    input {
      color: @error-color;
    }
  }
}
</style>
<script>
export default {
  props: {
    title: {
      default: ""
    },
    type: {
      default: "text" // number password text
    },
    value: {
      default: ""
    },
    disabled: {
      default: "false"
    },
    placeholder: {
      default: ""
    },
    autofocus: {
      default: false
    },
    right: {
      default: false
    },
    mode: {
      default: "normal" // normal wrapped
    },
    error: {
      default: false
    },
    maxlength: {
      default: -1
    }
  },

  data() {
    return {
      my_value: this.value
    };
  },

  onInit() {
    this.$watch("value", "watchValue");
  },

  onReady() {
    const focus = this.autofocus.toString() === "true";
    if (!focus) return; // 解决拉不起输入法的问题
    this.$element("input").focus({ focus: focus });
  },

  handleInputChange(event) {
    this.my_value = event.value;

    this.$emit("change", event);
  },

  handleInputFocus(event) {
    this.$emit("focus", event);
  },

  handleInputBlur(event) {
    this.$emit("blur", event);
  },

  watchValue(newV) {
    this.my_value = newV;
  }
};
</script>
