<template>
  <div class="page">
    <div class="page-prev" if="type === 'button'" onclick="clickPage('prev')">
      <div class="page-btn {{current === 1 ? 'disabled' : ''}}">
        <text class="{{current === 1 ? 'disabled-text' : ''}}">{{
          prevText
        }}</text>
      </div>
    </div>
    <!-- 显示数字 -->
    <div class="page-number" if="type !== 'pointer' && !simple">
      <text class="current">{{ current }}</text
      ><text>/{{ total }}</text>
    </div>
    <!-- 显示点 -->
    <div class="page-pointer" if="type === 'pointer'">
      <div
        class="page-pointer-dot {{ ($idx + 1) === current ? 'current' : '' }}"
        for="{{totalList}}"
        key="$idx"
      ></div>
    </div>
    <div class="page-next" if="type === 'button'" onclick="clickPage('next')">
      <div class="page-btn {{current === total ? 'disabled' : ''}}">
        <text class="{{current === total ? 'disabled-text' : ''}}">{{
          nextText
        }}</text>
      </div>
    </div>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";
.page {
  justify-content: space-between;
  margin: 10px * @ratio 0;

  &-btn {
    padding: 20px * @ratio 25px * @ratio;
    border: 1px solid @border-color-base;
    text {
      font-size: @text-size;
    }
  }
  .disabled {
    background-color: @btn-disable-bg;
    text {
      font-size: 30px * @ratio;
    }
    &-text {
      color: @btn-disable-color;
      font-size: 30px * @ratio;
    }
  }
  &-number {
    justify-content: center;
    flex: 1;
    text {
      color: #000000;
      font-weight: bold;
      font-size: @text-size;
    }
    .current {
      color: @primary-color;
      font-size: @text-size;
    }
  }
  &-pointer {
    width: 100%;
    height: @btn-circle-size;
    line-height: @btn-circle-size;
    margin: 0 auto;
    justify-content: center;
    &-dot {
      width: 16px * @ratio;
      height: 16px * @ratio;
      margin: 0 4px * @ratio;
      border-radius: 8px * @ratio;
      background-color: @btn-disable-color;
    }
    .current {
      background-color: @subsidiary-color;
      font-size: @text-size;
    }
  }
}
</style>

<script>
export default {
  data() {
    return {
      totalList: []
    };
  },
  props: {
    current: {
      type: Number,
      default: 1
    },
    total: {
      type: Number,
      default: 0
    },
    prevText: {
      type: String,
      default: "Prev"
    },
    nextText: {
      type: String,
      default: "Next"
    },
    // button || number || pointer
    type: {
      type: String,
      default: "button"
    },
    // 是否隐藏数值
    simple: {
      type: Boolean,
      default: false
    }
  },
  onInit() {
    this.totalList = Array.from({ length: this.total }, () => "");
  },
  clickPage(type) {
    if (this.current != 1 && type === "prev") {
      this.$emit("tap", { type });
    }
    if (this.current < this.total && type === "next") {
      this.$emit("tap", { type });
    }
  }
};
</script>
