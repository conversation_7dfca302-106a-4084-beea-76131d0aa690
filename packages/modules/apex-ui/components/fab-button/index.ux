<import name="my-icon" src="../icon/index"></import>
<import name="my-button" src="../button/index"></import>

<template>
  <stack class="fab-button fab-button-{{theme}}">
    <div
      class="fab-button-backdrop"
      show="{{ backdrop && buttonVisible }}"
    ></div>
    <block for="{{(index, button) in buttons }}">
      <div
        class="fab-button-button-wrap fab-button-{{position}} {{buttonStyle[index]}}"
      >
        <my-button
          class="fab-button-button fab-button-{{theme}} {{ button.disabled ? 'fab-button-button-disabled': ''}}"
          onclick="handleButtonClick(button)"
          icon="{{button.icon}}"
          disabled="{{button.disabled}}"
          inline="{{true}}"
          size="small"
        ></my-button>
        <text
          class="fab-button-button-label"
          if="{{button.label && direction === 'vertical' && buttonVisible}}"
          >{{ button.label }}</text
        >
      </div>
    </block>
    <div
      class="fab-button-action fab-button-{{position}} fab-button-{{clicked}}"
      onclick="handleClick"
    >
      <div if="{{actionButtonIcon}}">
        <my-icon
          type="{{actionButtonIcon}}"
          size="80"
          color="#ffffff"
        ></my-icon>
      </div>
      <div else>
        <slot name="action"></slot>
      </div>
    </div>
  </stack>
</template>

<script>
export default {
  data() {
    return {
      buttonVisible: false,
      notFirst: false
    };
  },

  props: {
    theme: {
      type: String,
      default: "balanced"
    },
    position: {
      type: String,
      default: "center"
    },
    actionButtonIcon: {
      type: String,
      default: "add"
    },
    actionButtonRotate: {
      type: Boolean,
      default: true
    },
    backdrop: {
      type: Boolean,
      default: false
    },
    buttons: {
      type: Array,
      default: []
    },
    direction: {
      type: String,
      default: "horizontal"
    },
    reverse: {
      type: Boolean,
      default: false
    },
    sAngle: {
      type: Number,
      default: 0
    },
    eAngle: {
      type: Number,
      default: 360
    }
  },

  computed: {
    clicked() {
      if (this.notFirst && this.actionButtonRotate) {
        if (this.buttonVisible) {
          return "clicked";
        } else {
          return "unclick";
        }
      } else {
        return "justopen";
      }
    },

    buttonStyle() {
      const arr = [];
      if (this.notFirst) {
        if (this.buttonVisible) {
          if (this.direction === "horizontal" && !this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-h${index}`;
              arr.push(style);
            });
          }
          if (this.direction === "horizontal" && this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-hr${index}`;
              arr.push(style);
            });
          }
          if (this.direction === "vertical" && !this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-v${index}`;
              arr.push(style);
            });
          }
          if (this.direction === "vertical" && this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-vr${index}`;
              arr.push(style);
            });
          }
          if (this.direction === "circle") {
            this.buttons.forEach((button, index) => {
              let style = `animation-c${index}`;
              arr.push(style);
            });
          }
        } else {
          if (this.direction === "horizontal" && !this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-h${index}close`;
              arr.push(style);
            });
          }
          if (this.direction === "horizontal" && this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-hr${index}close`;
              arr.push(style);
            });
          }
          if (this.direction === "vertical" && !this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-v${index}close`;
              arr.push(style);
            });
          }
          if (this.direction === "vertical" && this.reverse) {
            this.buttons.forEach((button, index) => {
              let style = `animation-vr${index}close`;
              arr.push(style);
            });
          }
          if (this.direction === "circle") {
            this.buttons.forEach((button, index) => {
              let style = `animation-c${index}close`;
              arr.push(style);
            });
          }
        }
      }
      return arr;
    }
  },

  handleButtonClick(button) {
    if (!button.disabled) {
      button.click();
    }
  },

  handleClick() {
    this.buttonVisible = !this.buttonVisible;
    this.notFirst = true;
  }
};
</script>

<style lang="less">
@import "../styles/base.less";

@light: #ddd;
@stable: #b2b2b2;
@positive: #387ef5;
@calm: #11c1f3;
@balanced: #33cd5f;
@energized: #ffc900;
@assertive: #ef473a;
@royal: #886aea;
@dark: #444;

.fab-button(@theme, @color) {
  &-@{theme},
  &-@{theme} &-action,
  &-@{theme} &-button {
    background-color: @color;
  }

  &-@{theme} &-button-disabled {
    background-color: desaturate(@color, 50%);
  }
}

.fab-button {
  position: fixed;

  &-center {
    left: 320px * @ratio;
    top: 600px * @ratio;
  }

  &-topLeft {
    left: 15px * @ratio;
    top: 15px * @ratio;
  }

  &-topRight {
    right: 15px * @ratio;
    top: 15px * @ratio;
  }

  &-bottomLeft {
    left: 15px * @ratio;
    bottom: 15px * @ratio;
  }

  &-bottomRight {
    right: 15px * @ratio;
    bottom: 15px * @ratio;
  }

  &-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
  }

  &-action {
    width: 100px * @ratio;
    height: 100px * @ratio;
    background-color: @balanced;
    border-radius: 100px * @ratio;
    align-items: center;
    justify-content: center;
    position: fixed;
  }

  &-button {
    width: 80px * @ratio;
    height: 80px * @ratio;
    margin: 10px;
    background-color: @balanced;
    border-radius: 120px * @ratio;
    align-items: center;
    justify-content: center;

    &-label {
      height: 50px * @ratio;
      margin-left: 20px * @ratio;
      font-size: 35px * @ratio;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.4);
      padding: 0 10px * @ratio;
      border-radius: 5px * @ratio;
    }

    &-wrap {
      position: fixed;
      align-items: center;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(135deg);
    }
  }

  @keyframes rotate2 {
    from {
      transform: rotate(135deg);
    }

    to {
      transform: rotate(0deg);
    }
  }

  &-clicked {
    animation-name: rotate;
    animation-duration: 0.2s;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
  }

  &-unclick {
    animation-name: rotate2;
    animation-duration: 0.2s;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
  }

  .fab-button(light, @light);
  .fab-button(stable, @stable);
  .fab-button(positive, @positive);
  .fab-button(calm, @calm);
  .fab-button(assertive, @assertive);
  .fab-button(balanced, @balanced);
  .fab-button(energized, @energized);
  .fab-button(royal, @royal);
  .fab-button(dark, @dark);
}

.animation(@item) {
  &-@{item} {
    animation-name: @item;
    animation-duration: 0.3s;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
  }
}

.keyframes(@direction, @offset) {
  @keyframes @direction {
    from {
      transform: scale(0) translateX(0px);
    }

    to {
      transform: scale(1) translateX(@offset);
    }
  }
}

.keyframes-close(@direction, @offset) {
  @keyframes @direction {
    from {
      transform: scale(1) translateX(@offset);
    }

    to {
      transform: scale(0) translateX(0px);
    }
  }
}

.keyframesY(@direction, @offset) {
  @keyframes @direction {
    from {
      transform: scale(0) translateY(0px);
    }

    to {
      transform: scale(1) translateY(@offset);
    }
  }
}

.keyframesY-close(@direction, @offset) {
  @keyframes @direction {
    from {
      transform: scale(1) translateY(@offset);
    }

    to {
      transform: scale(0) translateY(0px);
    }
  }
}

.animation {
  .keyframes(h0, 100px * @ratio);
  .keyframes(h1, 190px * @ratio);
  .keyframes(h2, 280px * @ratio);
  .keyframes(h3, 370px * @ratio);
  .keyframes-close(h0close, 100px * @ratio);
  .keyframes-close(h1close, 190px * @ratio);
  .keyframes-close(h2close, 280px * @ratio);
  .keyframes-close(h3close, 370px * @ratio);

  .keyframes(hr0, -100px * @ratio);
  .keyframes(hr1, -190px * @ratio);
  .keyframes(hr2, -280px * @ratio);
  .keyframes(hr3, -370px * @ratio);
  .keyframes-close(hr0close, -100px * @ratio);
  .keyframes-close(hr1close, -190px * @ratio);
  .keyframes-close(hr2close, -280px * @ratio);
  .keyframes-close(hr3close, -370px * @ratio);

  .keyframesY(v0, 100px * @ratio);
  .keyframesY(v1, 190px * @ratio);
  .keyframesY(v2, 280px * @ratio);
  .keyframesY(v3, 370px * @ratio);
  .keyframesY-close(v0close, 100px * @ratio);
  .keyframesY-close(v1close, 190px * @ratio);
  .keyframesY-close(v2close, 280px * @ratio);
  .keyframesY-close(v3close, 370px * @ratio);

  .keyframesY(vr0, -100px * @ratio);
  .keyframesY(vr1, -190px * @ratio);
  .keyframesY(vr2, -280px * @ratio);
  .keyframesY(vr3, -370px * @ratio);
  .keyframesY-close(vr0close, -100px * @ratio);
  .keyframesY-close(vr1close, -190px * @ratio);
  .keyframesY-close(vr2close, -280px * @ratio);
  .keyframesY-close(vr3close, -370px * @ratio);

  .keyframes(c0, 100px * @ratio);
  .keyframesY(c1, -100px * @ratio);
  .keyframes(c2, -100px * @ratio);
  .keyframesY(c3, 100px * @ratio);
  .keyframes-close(c0close, 100px * @ratio);
  .keyframesY-close(c1close, -100px * @ratio);
  .keyframes-close(c2close, -100px * @ratio);
  .keyframesY-close(c3close, 100px * @ratio);

  .animation(h0);
  .animation(h1);
  .animation(h2);
  .animation(h3);
  .animation(h0close);
  .animation(h1close);
  .animation(h2close);
  .animation(h3close);

  .animation(hr0);
  .animation(hr1);
  .animation(hr2);
  .animation(hr3);
  .animation(hr0close);
  .animation(hr1close);
  .animation(hr2close);
  .animation(hr3close);

  .animation(v0);
  .animation(v1);
  .animation(v2);
  .animation(v3);
  .animation(v0close);
  .animation(v1close);
  .animation(v2close);
  .animation(v3close);

  .animation(vr0);
  .animation(vr1);
  .animation(vr2);
  .animation(vr3);
  .animation(vr0close);
  .animation(vr1close);
  .animation(vr2close);
  .animation(vr3close);

  .animation(c0);
  .animation(c1);
  .animation(c2);
  .animation(c3);
  .animation(c0close);
  .animation(c1close);
  .animation(c2close);
  .animation(c3close);
}
</style>
