<template>
  <div class="apex-image">
    <stack>
      <div class="image-wrap">
        <image
          class="apex-image-{{shape}}"
          style="width:{{width}};height:{{height}}"
          src="{{src}}"
          oncomplete="handleLoadedImage"
          onerror="handleError"
          if="{{src}}"
        ></image>
        <div else>
          <slot></slot>
        </div>
      </div>
      <div
        class="empty"
        style="width:{{width}};height:{{height}}"
        if="{{ status === 'empty' }}"
      >
        <text if="{{empty}}">{{ empty }}</text>
        <div else>
          <slot name="empty"></slot>
        </div>
      </div>
      <div
        class="loading"
        style="width:{{width}};height:{{height}}"
        elif="{{ status === 'loading' }}"
      >
        <text class="loading-text" if="{{loading}}">{{ loading }}</text>
        <div else>
          <slot name="loading"></slot>
        </div>
      </div>
      <div
        class="error"
        style="width:{{width}};height:{{height}}"
        elif="{{ status === 'error' }}"
      >
        <text class="error-text" if="{{error}}">{{ error }}</text>
        <div else>
          <slot name="error"></slot>
        </div>
      </div>
    </stack>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

.apex-image {
  flex-direction: column;
  &-rounded {
    border-radius: 10px * @ratio;
  }
  &-circle {
    border-radius: 1000px * @ratio;
  }
  &-thumbnail {
    background-color: #ffffff;
    padding: 10px * @ratio;
    border: 1px solid #eee;
  }
}

.empty {
  background-color: #eee;
  justify-content: center;
  text {
    font-size: 50px * @ratio;
    font-weight: bold;
  }
}

.error {
  background-color: #eee;
  justify-content: center;
  text {
    font-size: 50px * @ratio;
    font-weight: bold;
  }
}

.loading {
  background-color: #eee;
  justify-content: center;
  text {
    font-size: 50px * @ratio;
    font-weight: bold;
  }
}
</style>

<script>
export default {
  data() {
    return {
      status: !!this.src ? "loading" : "empty"
    };
  },

  props: {
    src: {
      type: String,
      default: ""
    },
    shape: {
      type: String,
      default: "normal" //normal|round|circle|thumbnail
    },
    width: {
      type: String,
      default: "300px"
    },
    height: {
      type: String,
      default: "300px"
    },
    loading: {
      type: String,
      default: ""
    },
    empty: {
      type: String,
      default: ""
    },
    error: {
      type: String,
      default: ""
    }
  },

  handleLoadedImage() {
    this.updateStatus("loaded");
  },

  handleError() {
    this.updateStatus("error");
  },

  updateStatus(status) {
    if (this.status !== status) {
      this.status = status;
    }
  },

  onShow() {
    console.log("status", this.status);
  }
};
</script>
