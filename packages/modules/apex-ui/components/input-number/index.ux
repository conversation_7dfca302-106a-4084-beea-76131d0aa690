<template>
  <div class="apex-input-number">
    <text
      class="apex-input-number-minus {{ my_value <= my_min ? 'apex-input-number-disabled' : '' }}"
      onclick="handleMinus"
      if="{{ !positive || my_value > 0 }}"
      >-
    </text>
    <input
      class="apex-input-number-text {{ my_min >= my_max ? 'apex-input-number-disabled' : '' }}"
      type="number"
      value="{{ my_value }}"
      disabled="{{ my_min >= my_max }}"
      onchange="handleChange"
      if="{{ !positive || my_value > 0 }}"
    />
    <text
      class="apex-input-number-plus {{ my_value >= my_max ? 'apex-input-number-disabled' : '' }}"
      onclick="handlePlus"
      >+
    </text>
  </div>
</template>
<style lang="less">
@import "../styles/base";

.apex-input-number {
  color: @text-color;
  flex-direction: row;
  align-self: baseline;

  text {
    height: 60px * @ratio;
    width: 60px * @ratio;
    text-align: center;
    font-size: @size-font-small;
    border: 1px solid @border-color-base;
  }

  &-minus {
    border-radius: 2px * @ratio;
  }
  &-plus {
    border-radius: 2px * @ratio;
  }

  &-text {
    border-top-width: 1px;
    border-bottom-width: 1px;
    border-style: solid;
    border-top-color: #dddee1;
    border-bottom-color: #dddee1;
    text-align: center;
    height: 60px * @ratio;
    line-height: 60px * @ratio;
    width: 60px * @ratio;
    font-size: @size-font-small;
  }

  &-disabled {
    border-color: @btn-disable-border;
    color: @btn-disable-color;
    background-color: @btn-disable-bg;
  }
}
</style>
<script>
import { addNum } from "../common/scripts/util";

export default {
  props: {
    value: {
      default: 1
    },
    min: {
      default: -Infinity
    },
    max: {
      default: Infinity
    },
    step: {
      default: 1
    },
    positive: {
      default: false
    }
  },

  data() {
    return {
      my_value: this.value,
      my_step: this.step,
      my_min: this.min,
      my_max: this.max
    };
  },

  handleChangeStep(type) {
    let disabled = false;
    if (this.my_value <= this.my_min && type === "minus") {
      disabled = true;
    } else if (this.my_value >= this.my_max && type === "plus") {
      disabled = true;
    }
    if (disabled) return;
    if (type === "minus") {
      this.my_value = addNum(this.my_value, -this.my_step);
    } else if (type === "plus") {
      this.my_value = addNum(this.my_value, this.my_step);
    }
    if (this.my_value > this.my_max) {
      this.my_value = this.my_max;
    } else if (this.my_value < this.my_min) {
      this.my_value = this.my_min;
    }
    this.handleEmit(this.my_value, type);
  },

  handleMinus(e) {
    this.handleChangeStep("minus");
  },

  handlePlus(e) {
    this.handleChangeStep("plus");
  },

  handleChange(e) {
    let value = e.value;
    const { my_min, my_max } = this;
    if (value > my_max) {
      value = my_max;
    } else if (value < my_min) {
      value = my_min;
    }
    this.my_value = value;

    this.handleEmit(value);
  },

  handleEmit(value, type) {
    const data = {
      value: value
    };
    if (type) data.type = type;

    this.$emit("change", data);
  }
};
</script>
