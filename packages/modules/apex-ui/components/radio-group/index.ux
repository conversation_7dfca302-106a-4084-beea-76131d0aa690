<template>
  <div class="apex-radio-group">
    <slot></slot>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

.apex-radio-group {
  flex-direction: column;
}
</style>

<script>
export default {
  data() {
    return {};
  },

  props: {
    current: {
      default: ""
    },
    id: {
      type: String,
      required: true,
      default: ""
    }
  },

  onInit() {
    this.$watch("current", "changeCurrent");
  },

  changeCurrent(val = this.current) {
    const radios = this.radios;
    if (radios && radios.length > 0) {
      radios.forEach(item => {
        item.changeCurrent(val === item.value);
      });
    }
  },
  emitEvent(params) {
    this.$emit("change", params);
  }
};
</script>
