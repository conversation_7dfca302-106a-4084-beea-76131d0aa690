<template>
  <div class="apex-popover">
    <div class="mask" if="{{visible}}" onclick="showMask"></div>
    <div
      if="{{visible}}"
      onclick="showMask"
      style="{{boxStyle}}"
      class="box {{boxClassX}} {{boxClassY}}"
    >
      <div class="frame">
        <div
          class="body"
          style="background-color:{{normal}};width:{{bodyWidth}}px;height:{{bodyHeight}}px"
        >
          <div class="item-list">
            <div onclick="clickHandler">
              <text class="item" if="{{title}}">{{ title }}</text>
              <slot name="title" else></slot>
            </div>
            <div onclick="clickHandler">
              <text class="item item-line" if="{{content}}">{{ content }}</text>
              <slot name="content" else></slot>
            </div>
          </div>
        </div>
      </div>
      <!-- 箭头 -->
      <div
        class="arrows {{arrowsClass}}"
        style="{{styleObj}}"
        if="{{flag===true}}"
      ></div>
    </div>
    <div ontouchstart="handler" style="{{pixStyle}}">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import prompt from "@system.prompt";
export default {
  data() {
    return {
      bodyWidth: 200,
      bodyHeight: 160,
      visible: false,
      hori: 0,
      right: 0,
      left: 0,
      boxHeight: 200,
      boxWidth: 240,
      normal: "#ffffff",
      arrowsClass: "", // 箭头的class
      styleObj: {}, // 箭头的style
      flag: false, // 是否为当前箭头
      pixStyle: {}, // 自定义margin
      boxClassX: "",
      boxClassY: "",
      boxStyle: {}
    };
  },
  props: {
    popoverWidth: {
      default: 0
    },
    popoverHeight: {
      defualt: 0
    },
    orientation: {
      default: "top"
    },
    title: {
      default: ""
    },
    content: {
      default: ""
    },
    size: {
      type: Array,
      default: [] //slot中自定义元素宽高
    },
    pixClass: {
      defualt: ""
    },
    popoverColor: {
      defualt: ""
    },
    pixMargin: {
      defualt: 20
    }
  },
  onInit() {
    let top = this.pixMargin ? `${this.pixMargin}px` : "20px";
    this.pixStyle = {
      marginTop: top
    };
  },
  handler(evt) {
    let height = this.size[1]; // slot自定义元素的高
    let hori;
    if (this.popoverColor) {
      this.normal = this.popoverColor;
    }
    // 限制最小气泡宽高
    if (this.popoverWidth > 200) {
      this.bodyWidth = parseInt(this.popoverWidth);
      this.boxWidth = parseInt(this.popoverWidth) + 40;
    }
    if (this.popoverHeight > 160) {
      this.bodyHeight = parseInt(this.popoverHeight);
      this.boxHeight = parseInt(this.popoverHeight) + 40;
    }
    // 上左,下左,上右,下右
    if (this.orientation.indexOf("Right") !== -1) {
      this.left =
        parseInt(evt.touches[0].clientX) -
        parseInt(evt.touches[0].offsetX) -
        20;
      this.styleObj = { marginRight: `-${this.bodyWidth - 80}px` };
      this.boxStyle.right = `${this.left}px`;
    }
    if (this.orientation.indexOf("Left") !== -1) {
      this.right =
        parseInt(evt.touches[0].clientX) -
        parseInt(evt.touches[0].offsetX) -
        20;
      this.styleObj = { marginLeft: `-${this.bodyWidth - 80}px` };
      this.boxStyle.left = `${this.right}px`;
    }
    if (this.orientation.indexOf("top") !== -1) {
      hori =
        parseInt(evt.touches[0].clientY) -
        parseInt(evt.touches[0].offsetY) -
        this.bodyHeight -
        40;
      this.boxStyle.top = `${hori}px`;
      this.arrowsClass = "arrows-top";
      this.boxClassY = "box-vertical-up";
    }
    if (this.orientation.indexOf("bottom") !== -1) {
      hori =
        parseInt(evt.touches[0].clientY) -
        parseInt(evt.touches[0].offsetY) +
        height;
      this.boxStyle.top = `${hori}px`;
      this.arrowsClass = "arrows-bottom";
      this.boxClassY = "box-vertical-down";
    }
    if (this.orientation === "top" || this.orientation === "bottom") {
      let width = this.size[0];
      if (width <= this.bodyWidth) {
        this.left =
          parseInt(evt.touches[0].clientX) -
          parseInt(evt.touches[0].offsetX) -
          (this.bodyWidth - width) / 2 -
          20;
      } else {
        this.left =
          parseInt(evt.touches[0].clientX) -
          parseInt(evt.touches[0].offsetX) +
          (width - this.bodyWidth) / 2 -
          20;
      }
      this.boxStyle.left = `${this.left}px`;
    }
    // 左上,左下,右上,右下
    if (
      this.orientation.indexOf("right") !== -1 ||
      this.orientation.indexOf("left") !== -1
    ) {
      this.styleObj.marginTop = "30px";
      if (this.orientation.indexOf("right") !== -1) {
        this.boxClassX = "box-horizontal";
        this.arrowsClass = "arrows-right";
        this.boxStyle.left = `${this.size[0] + 20}px`;
      } else {
        this.boxStyle.right = `${this.size[0] + 20}px`;
        this.arrowsClass = "arrows-left";
      }
      this.bodyHeight < height
        ? (hori =
            parseInt(evt.touches[0].clientY) -
            parseInt(evt.touches[0].offsetY) +
            (height - this.bodyHeight) / 2)
        : (hori =
            parseInt(evt.touches[0].clientY) -
            parseInt(evt.touches[0].offsetY) -
            this.bodyHeight / 2);
      this.boxStyle.top = `${hori}px`;
    }
    if (this.orientation.indexOf("Top") !== -1) {
      hori =
        parseInt(evt.touches[0].clientY) - parseInt(evt.touches[0].offsetY);
      this.boxStyle.top = `${hori}px`;
      this.styleObj.marginTop = "40px";
      this.styleObj.alignSelf = "flex-start";
    }
    if (this.orientation.indexOf("Bottom") !== -1) {
      hori =
        parseInt(evt.touches[0].clientY) -
        parseInt(evt.touches[0].offsetY) -
        this.bodyHeight +
        height / 2;
      this.boxStyle.top = `${hori}px`;
      this.styleObj.marginBottom = "40px";
      this.styleObj.alignSelf = "flex-end";
    }
    this.boxStyle.height = `${this.boxHeight}px`;
    this.boxStyle.width = `${this.boxWidth}px`;
    this.styleObj.backgroundColor = this.normal;
    this.getOrientation();
    this.visible = true;
  },
  showMask() {
    this.visible = false;
  },
  // 阻止冒泡
  clickHandler(evt) {
    if (this.visible === true) {
      this.visible = false;
    }
    if (this.visible === false) {
      this.visible = true;
    }
    evt.stopPropagation();
  },
  getOrientation() {
    switch (this.orientation) {
      case "top":
        this.flag = true;
        break;
      case "topRight":
        this.flag = true;
        break;
      case "topLeft":
        this.flag = true;
        break;
      case "bottom":
        this.flag = true;
        break;
      case "bottomRight":
        this.flag = true;
        break;
      case "bottomLeft":
        this.flag = true;
        break;
      case "left":
        this.flag = true;
        break;
      case "leftTop":
        this.flag = true;
        break;
      case "leftBottom":
        this.flag = true;
        break;
      case "right":
        this.flag = true;
        break;
      case "rightTop":
        this.flag = true;
        break;
      case "rightBottom":
        this.flag = true;
        break;

      default:
        break;
    }
  }
};
</script>
<style lang="less">
@import "../styles/base.less";
.mask {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  width: 100%;
}
.box {
  align-items: center;
  position: fixed;
  justify-content: flex-end;
  &-vertical-up {
    flex-direction: column;
  }
  &-vertical-down {
    flex-direction: column-reverse;
  }
  &-horizontal {
    flex-direction: row-reverse;
  }
  .frame {
    background-image: url("./img/shadows.9.png");
    padding: 5px * @ratio;
    .body {
      border-radius: 10px * @ratio;
      border: 1px solid #e4e4e4;
      flex-direction: column;
      .item-list {
        flex-direction: column;
        width: 100%;
        height: 100%;
        .item {
          width: 100%;
          height: 80px * @ratio;
          lines: 1;
          text-overflow: ellipsis;
          padding-left: 20px * @ratio;
          &-line {
            border-top: 1px solid #e4e4e4;
          }
        }
      }
    }
  }
  .arrows {
    width: 20px * @ratio;
    height: 20px * @ratio;
    transform: rotate(45deg);
    border: 1px solid #e4e4e4;
    &-top {
      margin-top: -15px * @ratio;
      border-top-color: transparent;
      border-left-color: transparent;
      margin-bottom: 10px * @ratio;
    }
    &-bottom {
      margin-bottom: -15px * @ratio;
      border-bottom-color: transparent;
      border-right-color: transparent;
      margin-top: 10px * @ratio;
    }
    &-left {
      margin-left: -15px * @ratio;
      border-bottom-color: transparent;
      border-left-color: transparent;
      margin-right: 10px * @ratio;
    }
    &-right {
      margin-right: -15px * @ratio;
      border-top-color: transparent;
      border-right-color: transparent;
      margin-left: 10px * @ratio;
    }
  }
}
</style>
