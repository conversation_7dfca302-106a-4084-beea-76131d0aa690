<import name="my-icon" src="../icon/index"></import>
<template>
  <div class="apex-search-bar">
    <div class="{{pixClass}}">
      <div class="box">
        <my-icon type="search" size="36" color="#afafaf"></my-icon>
        <input
          id="input"
          style="text-align:{{mask?'center':'left'}};placeholder-color:{{placeholderColor}}"
          enterkeytype="{{enterkeytype}}"
          type="text"
          value="{{value}}"
          onchange="bindChange"
          onfocus="bindFocus"
          onblur="bindBlur"
          maxlength="{{maxlength}}"
          placeholder="{{placeholder}}"
          disabled="{{disabled}}"
          onenterkeyclick="enterEvent"
          onselectionchange="bindSelectionchange"
        />
        <my-icon
          type="close-circle"
          size="36"
          color="#afafaf"
          onclick="bindClear"
          if="{{clearIcon&&clear}}"
        ></my-icon>
      </div>
      <text class="cancel" if="{{cancel}}" onclick="bindCancel">{{
        cancelText
      }}</text>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    mask: true,
    clearIcon: false,
    value: "",
    cancel: false
  },
  props: {
    hasCancel: {
      type: Boolean,
      default: false
    },
    placeholderColor: {
      type: String,
      default: "rgb(128, 128, 128)"
    },
    placeholder: {
      type: String,
      default: "搜索"
    },
    maxlength: {
      type: Number
    },
    pixClass: {
      type: String,
      default: "wrap"
    },
    disabled: {
      type: Boolean,
      default: false
    },
    cancelText: {
      type: String,
      default: "取消"
    },
    inputFocus: {
      type: Boolean,
      default: false
    },
    inputValue: {
      type: String,
      default: ""
    },
    clear: {
      type: Boolean,
      default: true
    },
    select: {
      type: Boolean,
      default: false
    },
    selectionStart: {
      type: Number
    },
    selectionEnd: {
      type: Number
    },
    enterkeytype: {
      type: String,
      default: "defualt"
    }
  },
  onInit() {
    if (this.hasCancel) {
      this.cancel = this.hasCancel;
    }
    if (this.inputValue) {
      this.value = this.inputValue;
    }
  },
  onReady() {
    if (this.inputFocus === true && this.disabled === false) {
      this.mask = false;
      this.$element("input").focus({ focus: true });
    }
    if (this.select === true) {
      this.$element("input").select();
    }
    if (this.selectionStart !== undefined && this.selectionEnd !== undefined) {
      this.$element("input").setSelectionRange({
        start: this.selectionStart,
        end: this.selectionEnd
      });
    }
  },
  bindFocus(evt) {
    this.$emit("focus", { event: evt });
    this.cancel = true;
    this.mask = false;
  },
  bindBlur(evt) {
    this.$emit("blur", { event: evt });
    if (!this.hasCancel) {
      this.cancel = false;
    }
    if (this.clear === true && this.disabled === false) {
      this.clearIcon = false;
    }
    this.value = "";
    this.mask = true;
    this.$element("input").focus({ focus: false });
  },
  bindClear() {
    this.$emit("clear", { inputValue: this.value });
    this.inputValue ? (this.value = this.inputValue) : (this.value = "");
  },
  bindChange(evt) {
    this.$emit("change", { event: evt });
    this.value = evt.value;
    if (this.value !== "") {
      this.clearIcon = true;
    }
  },
  bindCancel() {
    this.$emit("cancel");
    this.bindBlur();
  },
  enterEvent(evt) {
    this.$emit("enterkeyclick", { event: evt });
  },
  bindSelectionchange(evt) {
    this.$emit("selectionchange", { event: evt });
  }
};
</script>

<style lang="less">
@import "../styles/base.less";
.wrap {
  background-color: #efeff4;
  height: 80px * @ratio;
  width: 100%;
  padding: 16px * @ratio 20px * @ratio;
  align-items: center;
  .box {
    background-color: #fff;
    width: 100%;
    height: 100%;
    border-radius: 8px * @ratio;
    align-items: center;
    padding: 0 10px * @ratio;
    input {
      flex: 1;
      font-size: 30px * @ratio;
      margin-left: 10px * @ratio;
      background-color: #fff;
    }
    .default {
      margin-left: 10px * @ratio;
      color: #808080;
      text-align: center;
    }
  }
}
.cancel {
  width: 10%;
  text-align: center;
  color: #2dc250;
  margin-left: 20px * @ratio;
  font-size: @text-size;
}
</style>
