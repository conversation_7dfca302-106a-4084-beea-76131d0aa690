<import name="my-mask" src="../mask/index.ux"></import>

<template>
  <div class="wrap" style="{{disabled?'opacity: 0.3;':''}}">
    <div class="min" if="{{showMin}}">
      <text class="min-value" if="{{showMinValue}}">{{ min }}</text>
      <block else>
        <slot name="min"></slot>
      </block>
    </div>

    <slider
      class="slider"
      style="{{styleObject | filterStyle}}"
      min="{{min}}"
      max="{{max}}"
      step="{{step}}"
      value="{{currentValue}}"
      onchange="onchange"
      ontouchstart="touchstartOfSlider"
      ontouchmove="touchmoveOfSlider"
      ontouchend="touchendOfSlider"
      ontouchcancel="touchcancelOfSlider"
    ></slider>

    <div class="max" if="{{showMax}}">
      <text class="max-value" if="{{showMaxValue}}">{{ max }}</text>
      <block else>
        <slot name="max"></slot>
      </block>
    </div>

    <div
      class="value_tips_wrap"
      show="{{showTips && showTipsValue}}"
      ontouchstart="touchstartOfBackdrop"
    >
      <my-mask id="mask" transparent="{{true}}"></my-mask>
      <text
        class="value_tips"
        style="top: {{tipsTop}}px; left: {{tipsLeft}}px;"
        >{{ currentValue | filterCurrentValue }}</text
      >
    </div>

    <div class="disabled" if="{{disabled}}"></div>
  </div>
</template>
<style lang="less">
@import "../styles/base.less";

.wrap {
  flex-direction: row;
  align-items: center;
  position: relative;
  .min,
  .max {
    &-value {
      font-size: 30px * @ratio;
    }
  }
  .value_tips_wrap {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    .value_tips {
      lines: 1;
      padding: 8px * @ratio 16px * @ratio;
      border-radius: 8px * @ratio;
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
      font-size: 30px * @ratio;
      position: absolute;
      z-index: 1000;
      transform: translate(-50%, -86px * @ratio);
    }
  }

  .slider {
    flex: 1;
    padding: 0 32px * @ratio;
  }
  .disabled {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
  }
}
</style>
<script>
export default {
  props: {
    min: {
      default: 0
    },
    max: {
      default: 100
    },
    step: {
      default: 1
    },
    defaultValue: {
      default: 0
    },
    disabled: {
      default: false
    },
    valueType: {
      default: "none"
    },
    tipsFormatter: {
      default: "{d}"
    },
    showTips: {
      default: true
    },
    styleObject: {
      default: {
        color: "#f0f0f0",
        "selected-color": "#009688",
        "block-color": "#009688"
      }
    }
  },
  data() {
    return {
      showMin: false,
      showMax: false,
      showMinValue: false,
      showMaxValue: false,
      showTipsValue: false,
      tipsTop: 0,
      tipsLeft: 0,
      currentValue: 0
    };
  },
  onInit() {
    this.currentValue = this.defaultValue;
    this.initValue();
  },
  initValue() {
    if (typeof this.valueType == "object") {
      Object.keys(this.valueType).forEach(key => {
        if (key == "min" && this.valueType["min"]) {
          switch (this.valueType["min"]) {
            case "value":
              this.showMin = true;
              this.showMinValue = true;
              break;
            case "custom":
              this.showMin = true;
              this.showMinValue = false;
              break;
          }
        } else if (key == "max" && this.valueType["max"]) {
          switch (this.valueType["max"]) {
            case "value":
              this.showMax = true;
              this.showMaxValue = true;
              break;
            case "custom":
              this.showMax = true;
              this.showMaxValue = false;
              break;
          }
        }
      });
    } else {
      switch (this.valueType) {
        case "value":
          this.showMin = true;
          this.showMax = true;
          this.showMinValue = true;
          this.showMaxValue = true;
          break;
        case "custom":
          this.showMin = true;
          this.showMax = true;
          this.showMinValue = false;
          this.showMaxValue = false;
          break;
      }
    }
  },
  filterStyle(styleObject) {
    return Object.keys(styleObject)
      .map(key => {
        return `${key}: ${styleObject[key]};`;
      })
      .join(" ");
  },
  filterCurrentValue(currentValue) {
    return this.tipsFormatter.replace(/{d}/g, currentValue);
  },
  onchange(e) {
    this.currentValue = e.progress;
    this.$emit("change", { currentValue: this.currentValue });
  },
  toggleTips(status) {
    if (status == "show") {
      this.showTipsValue = true;
      this.$child("mask").show();
    } else {
      this.showTipsValue = false;
      this.$child("mask").hide();
      this.$emit("afterChange", { currentValue: this.currentValue });
    }
  },
  touchstartOfSlider(e) {
    if (!!e["_touches"]) {
      // 联盟规范
      this.tipsTop = e["_touches"][0]["clientY"];
      this.tipsLeft = e["_touches"][0]["clientX"];
    } else if (!!e["touches"]) {
      // 华为规范
      this.tipsTop = e["touches"][0]["clientY"];
      this.tipsLeft = e["touches"][0]["clientX"];
    }
  },
  touchmoveOfSlider(e) {
    if (!!e["_touches"]) {
      this.tipsLeft = e["_touches"][0]["clientX"];
    } else if (!!e["touches"]) {
      this.tipsLeft = e["touches"][0]["clientX"];
    }
    this.toggleTips("show");
  },
  touchendOfSlider(e) {
    this.toggleTips("hide");
  },
  touchcancelOfSlider() {
    this.toggleTips("hide");
  },
  touchstartOfBackdrop() {
    this.toggleTips("hide");
  }
};
</script>
