<template>
  <div class="segmentroot">
    <div
      class="segment {{ 'segment-'+theme }} {{ disabled ? 'segment-disabled' : '' }}"
      if="{{values.length}}"
    >
      
      <div
        for="values"
        onclick="change($idx)"
        class="segment__item {{ currentp === $idx ? 'segment__item-current' : '' }} {{ $idx === 0 ? 'segment__item-left' : $idx === values.length-1 ? 'segment__item-right' : $idx === values.length-2 && $idx != 0 ? 'segment__item-right2' : ''}}"
      >
        <text
          class="{{ currentp === $idx ? 'segment__item-current-text' : '' }}"
          >{{ $item }}</text
        >
      </div>
    </div>
  </div>
</template>

<style lang="less">
@import "../styles/base.less";

@light: #fff;
@stable: #f8f8f8;
@positive: #387ef5;
@calm: #11c1f3;
@balanced: #33cd5f;
@energized: #ffc900;
@assertive: #ef473a;
@royal: #886aea;
@dark: #444;

@light-inverse: #ddd;
@stable-inverse: #b2b2b2;
.segment(@theme, @color) {
  &-@{theme} &__item {
    color: @color;
    border-color: @color;

    &-current {
      background-color: @color;
      &-text {
        color: #fff;
        font-size: @text-size;
      }
    }
  }
}
.segmentroot {
  width: 100%;
  flex-direction: column;
}
.segment {
  width: 100%;
  display: flex;
  opacity: 1;
  margin: 10px * @ratio 0;

  &-disabled {
    opacity: 0.5;
  }
  &__item {
    flex: 1;
    justify-content: center;
    align-items: center;
    color: @balanced;
    font-size: 14px * @ratio;
    padding: 10px * @ratio 0;
    border: 1px solid @balanced;
    border-left-width: 0;

    &-left {
      border-left-width: 1px;
      border-top-left-radius: 8px * @ratio;
      border-bottom-left-radius: 8px * @ratio;
    }

    &-right {
      border-left-width: 1px;
      // border-right-width: 1px;
      border-top-right-radius: 8px * @ratio;
      border-bottom-right-radius: 8px * @ratio;
    }
    /* 同时设定最右的圆角和左边为0，则边框不显示，故hock */
    &-right2 {
      border-right-width: 0px;
    }

    &-current {
      background-color: @balanced;
      color: #fff;
    }

    text {
      font-size: @text-size;
    }
  }

  .segment(light, @light-inverse);
  .segment(stable, @stable-inverse);
  .segment(positive, @positive);
  .segment(calm, @calm);
  .segment(assertive, @assertive);
  .segment(balanced, @balanced);
  .segment(energized, @energized);
  .segment(royal, @royal);
  .segment(dark, @dark);
}
</style>

<script>
export default {
  data() {
    return {
      currentp: 0
    };
  },
  props: {
    theme: {
      type: String,
      default: "balanced"
    },
    values: {
      type: Array,
      default: []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    current: {
      type: Number,
      default: 0
    }
  },
  onInit() {
    this.currentp = this.current;
  },
  change(index) {
    if (!this.disabled) {
      this.$emit("change", { index });
      this.currentp = index;
    }
  }
};
</script>
