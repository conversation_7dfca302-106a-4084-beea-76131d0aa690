import router from '@system.router'

export const protectedObj = {
  bookId: null, // 小说id
  chapterId: null, // 章节id
}

export function initHandle(
  uri = 'pages/Content/Reader',
  // 非小说的话需要判断是否归因
  direct = false
) {
  console.log('initHandle', this.bookId, this.chapterId, direct)
  if (
    direct &&
    this.bookId &&
    this.chapterId &&
    this.bookId !== 'null' &&
    this.chapterId !== 'null'
  ) {
    router.push({
      uri,
      params: {
        bookId: Number(this.bookId),
        chapterId: Number(this.chapterId),
        ___PARAM_PAGE_ANIMATION___: {
          openEnter: `none`,
          closeEnter: `slide`,
          openExit: `slide`,
          closeExit: `slide`,
        },
      },
    })
  }
}
