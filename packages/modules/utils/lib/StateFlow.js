export class StateFlow {
  _value // 私有属性存储当前值
  _subscribers = new Set() // 使用 Set 存储订阅者，确保唯一性且方便移除

  /**
   * 创建一个新的 StateFlow 实例
   * @param {*} initialValue 初始值
   */
  constructor(initialValue) {
    this._value = initialValue
  }

  /**
   * 获取当前值
   */
  get value() {
    return this._value
  }

  /**
   * 设置新值。
   * 如果新值与当前值不同，则更新值并通知所有订阅者。
   * @param {*} newValue
   */
  set value(newValue) {
    if (this._value !== newValue) {
      // 简单的严格相等比较
      this._value = newValue
      this._notifySubscribers()
    }
  }

  /**
   * 更新当前值。提供一个函数，该函数接收当前值并返回新值。
   * 这是一个方便的方法，用于基于当前状态进行原子更新。
   * @param {function(*): *} updateFn - 一个接收当前值并返回新值的函数
   */
  update(updateFn) {
    this.value = updateFn(this.value)
  }

  /**
   * 通知所有订阅者当前值
   * @private
   */
  _notifySubscribers() {
    // 遍历 Set 中的订阅者并调用它们
    // 使用 for...of 遍历 Set 是安全的，即使在回调中取消订阅
    for (const subscriber of this._subscribers) {
      try {
        subscriber(this._value)
      } catch (error) {
        console.error('Error in subscriber:', error)
        // 你可能想在这里添加更复杂的错误处理逻辑
      }
    }
  }

  /**
   * 订阅值的变化。
   * @param {function(*): void} callback - 当值变化时调用的回调函数，接收新值作为参数。
   * @returns {function(): void} 一个用于取消订阅的函数。
   */
  subscribe(callback) {
    if (typeof callback !== 'function') {
      console.warn('Subscriber must be a function.')
      return () => {} // 返回一个空操作的取消订阅函数
    }

    this._subscribers.add(callback)
    // 新订阅者立即收到当前值
    try {
      callback(this._value)
    } catch (error) {
      console.error('Error in initial subscriber call:', error)
    }

    // 返回一个取消订阅的函数
    return () => {
      this._subscribers.delete(callback)
    }
  }

  /**
   * （可选）获取当前订阅者的数量
   */
  get subscriberCount() {
    return this._subscribers.size
  }
}
