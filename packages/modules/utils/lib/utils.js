import router from '@system.router'

const prompt = require('@system.prompt')
const sysStorage = require('@system.storage')
const request = require('@system.request')

import { fetch } from '@quickapp/business'
import { showDialog } from '@system.prompt'
import file from '@system.file'
import media from '@system.media'
import webview from '@system.webview'
// 注册信息
const REGISTER_INFO = 'REGISTER_INFO'

export const storage = {
  get(key) {
    return new Promise((resolve, reject) => {
      sysStorage.get({
        key: key,
        success: function (data) {
          if (data) {
            try {
              resolve(JSON.parse(data))
            } catch (e) {
              console.error('storage.get 报错了...', e)
              reject(e)
            }
          } else {
            resolve(null)
          }
        },
        fail: function (data, code) {
          console.log('storage.get' + JSON.stringify(data) + code)
          reject({ data, code })
        },
      })
    })
  },
  set(key, value) {
    return new Promise((resolve, reject) => {
      sysStorage.set({
        key: key,
        value: JSON.stringify(value),
        success: function (data) {
          resolve(data)
        },
        fail: function (data, code) {
          console.error('storage.set 报错了...')
          reject({ data, code })
        },
      })
    })
  },
  clear() {
    return new Promise((resolve, reject) => {
      sysStorage.clear({
        success() {
          resolve()
        },
        fail() {
          reject()
        },
      })
    })
  },
  delete(key) {
    return new Promise((resolve, reject) => {
      sysStorage.delete({
        key,
        success() {
          resolve()
        },
        fail() {
          reject()
        },
      })
    })
  },
}

/**
 * 拼接 url 和参数
 */
export function queryString(url, query) {
  let str = []
  for (let key in query) {
    str.push(key + '=' + query[key])
  }
  let paramStr = str.join('&')
  return paramStr ? `${url}?${paramStr}` : url
}

export function showToast(message = '', duration = 0) {
  if (!message) return
  prompt.showToast({
    message: message,
    duration,
  })
}

/**
 * 注册设备信息
 * @returns {*}
 */
function registerDeviceRequest(url) {
  return fetch({
    method: 'POST',
    url,
  })
}

export function getRegisterInfo() {
  return storage.get(REGISTER_INFO)
}

export function setRegisterInfo(value) {
  return storage.set(REGISTER_INFO, value)
}

/**
 * 注册设备信息
 * @returns {Promise<unknown>}
 */
export function registerDevice(registerUrl) {
  return new Promise(async (resolve, reject) => {
    try {
      // await createEvt();
      const info = await getRegisterInfo()
      if (!info || !info.accessToken) {
        const res = await registerDeviceRequest(registerUrl)
        await setRegisterInfo(res)
        resolve(res)
      } else {
        resolve(info)
      }
    } catch (e) {
      console.log('registerDevice error' + JSON.stringify(e))
      resolve(e)
    }
  })
}

/**
 * 创建请求函数
 * @param baseAPI 基础api
 * @param needRegister
 * @returns {(function({url?: *, data: *, method: *, header?: *}): Promise<unknown>)|*}
 */
export function createRequest(baseAPI, needRegister = true) {
  return async ({
    url,
    data,
    method,
    header = {},
    isUpload = false,
    files = [],
  }) => {
    try {
      let token = ''
      if (needRegister) {
        const { accessToken } = await getRegisterInfo()
        token = accessToken
      }

      const authorization = `Bearer ${token}`

      if (isUpload) {
        const uploadUrl = baseAPI + '/wallpaper/identify'
        return upload(uploadUrl, files, {
          ...data,
          token: authorization,
        })
      }

      return fetch({
        url: baseAPI + url,
        data,
        method,
        header: {
          Authorization: authorization,
          ...header,
        },
      })
    } catch (e) {
      // console.log('createRequest Error', e, JSON.stringify(e))
      return Promise.reject(e)
    }
  }
}

export function toPrivacyPage(uri = 'pages/Web', params = {}) {
  const webview = require('@system.webview')
  webview.loadUrl({
    url: __MANIFEST__.privacyUrl,
  })
}

export function toUserPage(uri = 'pages/Web', params = {}) {
  const webview = require('@system.webview')
  webview.loadUrl({
    url: __MANIFEST__.userUrl,
  })
}

export function toGuidelinesForTortClaims() {
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: __MANIFEST__.guidelinesForTortClaimsUrl,
    },
  })
}
export function toQuestionUrl() {
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: __MANIFEST__.questionUrl,
    },
  })
}
export function toServiceUrl() {
  router.push({
    uri: 'pages/web',
    params: {
      webSrc:
        __MANIFEST__.serviceUrl + '?url=' + encodeURI(__MANIFEST__.questionUrl),
    },
  })
}
export function toServiceUrl2() {
  const serviceUrl =
    __MANIFEST__.serviceUrl ||
    'https://app-h5.springtool.cn/static/views/service/novel.html'
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: serviceUrl + '?url=' + encodeURI(__MANIFEST__.questionUrl),
    },
  })
}
export function upload(url, files, params = {}) {
  return new Promise((resolve, reject) => {
    const data = []
    for (const key in params) {
      data.push({
        name: key,
        value: params[key],
      })
    }

    request.upload({
      method: 'POST',
      url,
      files: files,
      data: data,
      success: function (data) {
        console.log('success upload', JSON.stringify(data))

        let result = {}
        if (data && data.code === 200) {
          try {
            result = resolve(JSON.parse(data.data))
          } catch (e) {
            console.log('解析出错', e)
            reject({
              msg: result.message || '服务器错误',
              code: data.code,
            })
          }
        } else {
          console.log('data', data)
          reject({
            msg: result.message || '服务器错误',
            code: data.code,
          })
        }
      },
      fail: function (data, code) {
        console.log('fail', data, code)
        reject({
          msg: data,
          code,
        })
      },
    })
  })
}
export function log2File() {
  const DEBUG = process.env.NODE_ENV !== 'production'
  if (DEBUG) {
    let origLog = console.log
    let origInfo = console.info
    let origError = console.error
    let logs = ''

    function save2File(level, message, optionalParams) {
      logs +=
        level +
        ': ' +
        message +
        ' ' +
        (JSON.stringify(optionalParams) || '') +
        '\n'
      file.writeText({
        uri: 'internal://mass/log.txt',
        text: logs,
      })
    }

    console.info = function (message, optionalParams) {
      save2File('info', message, optionalParams)
      origInfo(...arguments)
    }
    console.error = function (message, optionalParams) {
      save2File('error', message, optionalParams)
      origError(...arguments)
    }
    console.log = function (message, optionalParams) {
      save2File('log', message, optionalParams)
      origLog(...arguments)
    }
  }
}

/**
 * 获取扩展名
 * @param str
 * @returns {string}
 */
export function getExtension(str) {
  const index = str.lastIndexOf('.')
  return str.substring(index)
}

/**
 * 随机字符串
 * @param len
 * @returns {string}
 */
export function randomString(len = 32) {
  /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
  const $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const maxPos = $chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}

export async function isFileExists(path) {
  return new Promise((resolve, reject) => {
    file.readText({
      uri: path,
      success: data => {
        resolve(true)
      },
      fail: (data, code) => {
        resolve(false)
      },
    })
  })
}

export function debugDialog(...args) {
  let output = ''
  for (let i = 0; i < args.length; i++) {
    output += String(args[i])
    if (i < args.length - 1) {
      output += ' '
    }
  }
  console.log(output)
  showDialog({
    message: output,
  })
}

export function shuffle(array) {
  const newArray = [...array]
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[newArray[i], newArray[j]] = [newArray[j], newArray[i]] // ES6 解构赋值交换元素
  }
  return newArray
}
