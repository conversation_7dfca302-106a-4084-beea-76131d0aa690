import uploadtask from '@system.uploadtask'
import { getExtension, randomString } from './utils'
import { getOssUploadInfo } from './api/ocr/ocr'
import { showDialog } from '@system.prompt'
import request from '@system.request'

/**
 * ocr key
 * @returns {string}
 */
export function genOssKey() {
  const date = new Date()
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const str = randomString()
  // key规则:ocr/年月日/随机文件名
  return 'ocr/' + year + month + day + '/' + str
}

// 这玩意打包后会报错
// export function oss(uri, key, policy, Signature) {
//   return new Promise(async (resolve, reject) => {
//     const extension = getExtension(uri)
//     const name = key + extension
//     const formData = {
//       key: name,
//       OSSAccessKeyId: 'LTAI5tM6BN1YYQ6xjoieqXkM',
//       policy,
//       Signature,
//     }
//
//     uploadtask.uploadFile({
//       url: 'https://inputmethod-dev.oss-cn-hangzhou.aliyuncs.com',
//       filePath: uri,
//       name: name,
//       formData,
//       success({ statusCode, data, headers }) {
//         const url =
//           'https://inputmethod-dev.oss-cn-hangzhou.aliyuncs.com/' + name
//         resolve({ statusCode, data: url, headers })
//       },
//       fail(err) {
//         reject(err)
//       },
//     })
//   })
// }

export function oss(uri, key, policy, Signature) {
  const extension = getExtension(uri)
  const name = key + extension

  return new Promise((resolve, reject) => {
    request.upload({
      url: 'https://inputmethod-dev.oss-cn-hangzhou.aliyuncs.com',
      files: [{ uri: uri }],
      data: [
        { name: 'key', value: name },
        { name: 'OSSAccessKeyId', value: 'LTAI5tM6BN1YYQ6xjoieqXkM' },
        { name: 'policy', value: policy },
        { name: 'Signature', value: Signature },
      ],
      success: function (data) {
        const url =
          'https://inputmethod-dev.oss-cn-hangzhou.aliyuncs.com/' + name
        resolve({ data: url })
        console.log('handling success', url, JSON.stringify(data))
      },
      fail: function (data, code) {
        showDialog({
          message: 'oss2 fail code: ' + JSON.stringify(data),
        })
        reject(data)
        console.log(`handling fail, code = ${code}`)
      },
    })
  })
}

export async function uploadOss(uri) {
  try {
    const { policy, signature } = await getOssUploadInfo()
    const key = genOssKey()
    return await oss(uri, key, policy, signature)
  } catch (e) {
    throw new Error(e)
  }
}
