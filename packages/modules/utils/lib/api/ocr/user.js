import { request } from '../../request'

/**
 * 用户信息
 * @param type
 * @returns {Promise<*>}
 */
export function getUserInfo(type = 1) {
  return request({
    method: 'GET',
    url: '/ocr/user/info?type=' + type,
  })
}

/**
 * 会员列表
 * @param type
 */
export function getVipList(type = 1) {
  return request({
    method: 'GET',
    url: '/ocr/vip/list',
  })
}

/**
 * 生成订单
 * @param id
 * @param payType
 * @returns {Promise<*>}
 */
export function generateOrder(id, payType) {
  return request({
    url: '/ocr/vip/generateOrder',
    method: 'post',
    data: {
      id,
      payType,
    },
  })
}
