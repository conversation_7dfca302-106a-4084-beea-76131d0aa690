{"name": "@quickapp/business", "version": "0.0.0", "description": "业务代码", "author": "guojun <<EMAIL>>", "homepage": "", "license": "ISC", "main": "lib/index.js", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "publishConfig": {"registry": "https://registry.npm.taobao.org"}, "repository": {"type": "git", "url": "https://codeup.aliyun.com/muchun/quickapp.git"}, "scripts": {"test": "echo \"Error: run tests from root\" && exit 1"}, "prettier": {"printWidth": 80, "tabWidth": 2, "semi": false, "singleQuote": true, "bracketSpacing": true, "trailingComma": "es5", "arrowParens": "avoid", "htmlWhitespaceSensitivity": "ignore"}, "devDependencies": {"crypto-js": "^4.1.1", "less": "^4.1.1", "less-loader": "^10.0.1"}, "dependencies": {"@quickapp/apex-ui": "^1.9.5"}}