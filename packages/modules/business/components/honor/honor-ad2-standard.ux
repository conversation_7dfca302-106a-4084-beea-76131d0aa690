<template>
  <div class="page-container">
    <picture-ad-root
      class="ad-item"
      if="{{adData && adData.adId}}"
      adid="{{adData.adId}}"
      ondislike="ondislike"
      onadshow="onadshow"
    >
      <div class="vertical">
        <picture-ad-click-callback-area
          adid="{{adData.adId}}"
          onareaclick="onareaclick"
        >
          <text class="ad-logo">广告</text>
          <text class="ad-title">{{ adData.title }}</text>
        </picture-ad-click-callback-area>
        <div class="video-container" if="{{adData.hasVideo}}">
          <picture-ad-click-callback-area
            class="vertical ad-video"
            adid="{{adData.adId}}"
            onareaclick="onareaclick"
            type="videoclick"
          ></picture-ad-click-callback-area>
        </div>
        <picture-ad-click-callback-area
          class="vertical"
          adid="{{adData.adId}}"
          onareaclick="onareaclick"
          else
        >
          <image
            src="{{image}}"
            class="ad-image"
            for="image in adData.imgUrlList"
            tid="image"
          ></image>
        </picture-ad-click-callback-area>
        <div class="ad-button-container">
          <ad-button
            class="ad-image-button"
            adid="{{adData.adId}}"
            height="50px"
            width="300px"
            font-size="25px"
            onbtnclick="onbtnclick"
            ondownloadstatuschange="ondownloadstatuschange"
          ></ad-button>
        </div>
      </div>
    </picture-ad-root>
  </div>
</template>

<script>
import ad from '@service.ad'

export default {
  data() {
    return {
      allowRecommend: true,
      adCount: 1,
      adData: {},
    }
  },
  props: {
    adid: {
      type: String,
    },
  },
  onInit() {
    this.preloadAd()
  },
  preloadAd() {
    console.log(`开始预加载广告 ${this.adid}`)
    ad.preloadAd({
      adUnitId: this.adid,
      allowRecommend: this.allowRecommend,
      type: 'native',
      adCount: this.adCount,
      success: res => {
        if (
          res.resultCode === 0 &&
          res.adInstanceList &&
          res.adInstanceList.length > 0
        ) {
          this.adData = res.adInstanceList[0]
        }
      },
      fail: res => {
        console.log(`❌ 预加载广告失败: ${JSON.stringify(res)}`)
      },
    })
  },
  onadshow(e) {
    console.log(`广告 ${this.adData.adId} 显示`)
    this.$emit('adshow')
  },
  ondislike(e) {
    this.adData = {}
  },
  onareaclick(e) {
    console.log(`广告 ${e.adid} 接收到点击事件`)
  },
  onbtnclick(e) {
    console.log(`广告 ${e.adid} / ${e.adUnitId} 接收到下载按钮点击事件`)
  },
  ondownloadstatuschange(e) {
    console.log(`广告 ${e.adid} / ${e.state} 接收到回调状态枚举`)
  },
}
</script>

<style lang="less">
.page-container {
  flex-direction: column;
}

.vertical {
  flex-direction: column;
}

.ad-item {
  flex-direction: column;
  padding: 32px 16px;
  justify-content: center;
  align-items: center;
}

.ad-logo {
  background-color: lightgray;
  padding: 4px 8px;
  margin: 0 8px;
  color: #ffffff;
}

.ad-title {
  lines: 1;

  max-width: 500px;
}

.ad-image {
  // width/height 此处不可用使用百分比
  width: 718px;
  height: 350px;
  margin: 32px 0;
  object-fit: contain;
}

.ad-video {
  width: 100%;
  height: 400px;
  margin: 32px 0;
}

.ad-button-container {
  flex-direction: column;
  justify-content: center;
  margin-bottom: 80px;
}

.video-container {
  flex-direction: column;
}
</style>
