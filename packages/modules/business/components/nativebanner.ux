<import
  name="ad2banner"
  src="@quickapp/business/components/vivo/vivo-ad2-banner.ux"
></import>
<import
  name="ad2bannerOppo"
  src="@quickapp/business/components/oppo/oppo-ad2-banner.ux"
></import>

<template>
  <stack class="item-container">
    <div
      class="container"
      if="{{native.isShow && !isVip}}"
      @touchstart="reportNativeClick"
    >
      <stack class="img-container">
        <video
          id="video"
          autoplay="true"
          class="ad-video"
          if="{{adData.videoUrlList && adData.videoUrlList[0]}}"
          src="{{adData.videoUrlList[0]}}"
        ></video>
        <image
          class="nativeImg"
          if="{{adData.imgUrlList && adData.imgUrlList[0]}}"
          src="{{adData.imgUrlList[0]}}"
        ></image>
        <image
          class="ad-logo"
          if="{{adData.logoUrl}}"
          src="{{adData.logoUrl}}"
        ></image>
        <text class="ad-logo-text" if="{{!adData.logoUrl}}">广告</text>
      </stack>
      <div class="text-container">
        <div class="title-container">
          <text class="text-container-title">{{ adData.title }}</text>
          <image
            src="http://embed-h5.fat.atyourservice.cn/quickapp_assets/images/ic_close.png"
            @touchstart="closeAd"
            class="ad-close"
          ></image>
        </div>
        <text class="text-desc">{{ adData.desc }}</text>
        <input
          class="adbtn"
          if="{{adData.clickBtnTxt}}"
          type="button"
          value="{{adData.clickBtnTxt}}"
          @touchstart="clickAdBtn"
        />
      </div>
    </div>
    <div
      class="container"
      if="{{native.isShow2 && !isVip && brand === 'oppo'}}"
    >
      <ad2bannerOppo
        ad-unit-id="{{adid}}"
        onload="loadHandler"
        onerror="errorHandler"
        onclose="closeAd"
        onadclick="clickAd"
      ></ad2bannerOppo>
    </div>
    <div
      class="container"
      if="{{native.isShow2 && !isVip && brand === 'vivo'}}"
    >
      <ad2banner
        ad-unit-id="{{adid}}"
        onload="loadHandler"
        onerror="errorHandler"
        onclose="closeAd"
        onadclick="clickAd"
      ></ad2banner>
    </div>
  </stack>
</template>
<style>
.container {
  flex-direction: row;
  width: 100%;
  height: 100%;
  align-items: center;
}

.img-container {
  width: 30%;
  height: 100%;
}

.nativeImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ad-logo {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 60px;
  height: 30px;
}

.ad-logo-text {
  position: absolute;
  left: 0;
  bottom: 0;
  background-color: #585858;
  color: #aaffffff;
  font-size: 18px;
  padding: 4px;
}

.text-container {
  flex: 1;
  height: 100%;
  flex-direction: column;
  padding-left: 16px;
  padding-right: 16px;
  justify-content: space-around;
}

.title-container {
  flex-direction: row;
}

.text-container-title {
  flex: 1;
}

.ad-close {
  margin-left: 8px;
  width: 50px;
  height: 50px;
}

.text-desc {
  margin-left: 15px;
  font-size: 20px;
  color: #444444;
}

.item-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  align-content: center;
}

.ad-video {
  object-fit: contain;
  width: 30%;
  height: 100%;
}

.adbtn {
  height: 50px;
  padding-left: 8px;
  padding-right: 8px;
  color: #ffffff;
  background-color: #00bfff;
  border-radius: 8px;
  align-self: flex-end;
}

.adbtn:active {
  background-color: #058fbd;
}
</style>
<script>
import ad from '@service.ad'
import adsdk from '../lib/adsdk'

import { trackEvent } from '../lib/index'
import device from '../lib/device'
import { showDialog } from '@system.prompt'
import globalData from '@quickapp/utils/lib/globalData'

let nativeAd
export default {
  data: {
    adid: null,
    adData: {},
    native: {
      isShow: false,
      isShow2: false,
    },
    brand: adsdk.provider(),
  },
  props: {
    eventName: {
      type: String,
    },
  },
  computed: {
    isVip() {
      return false
    },
  },
  onInit() {},
  onReady(options) {
    this.showNativeAd()
  },
  onShow(options) {
    if (this.native.isShow) {
      this.reportNativeShow()
    }
  },
  isDownloadAd(creativeType) {
    let downloadTypes = [103, 106, 107, 108, 101, 102, 110]
    return downloadTypes.includes(creativeType)
  },
  showNativeAd() {
    if (!this.adid) return
    try {
      if (!adsdk.isAdAvailable()) return
      if (this.isVip) return
      const provider = adsdk.provider()
      if (provider === 'vivo' || provider === 'oppo') {
        this.native.isShow = false
        this.native.isShow2 = true
      } else {
        if (nativeAd) {
          try {
            nativeAd.destroy()
          } catch (e) {}
        }
        nativeAd = ad.createNativeAd({ adUnitId: this.adid })
        nativeAd.onLoad(data => {
          console.info('ad data loaded: ' + JSON.stringify(data))
          this.adData = data.adList[0]
          if (this.adData) {
            this.native.isShow = true
            this.reportNativeShow()
          }
        })
        nativeAd.onError(e => {
          console.error('load ad error:' + JSON.stringify(e))
          this.native.isShow = false
          this.native.isShow2 = true
        })
        nativeAd.load()
      }
      trackEvent({
        category: 'advertise',
        action: 'request',
        opt_label: this.eventName,
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adid,
        },
      })
    } catch (e) {
      this.native.isShow = false
      this.native.isShow2 = true
    }
  },
  reportNativeShow() {
    if (nativeAd && this.native.isShow) {
      nativeAd.reportAdShow({ adId: this.adData.adId })

      trackEvent({
        category: 'advertise',
        action: 'exposure',
        opt_label: this.eventName,
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adid,
          ad_type: 6,
          ecpm: 0,
        },
      })
    }
  },
  reportNativeClick(evt) {
    nativeAd.reportAdClick({
      adId: this.adData.adId,
    })
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: this.eventName,
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adid,
      },
    })
    return false
  },
  listenNativeAdDownloadStatus(downloadstatus) {
    if (downloadstatus === 'INSTALLED') {
      this.adData.clickBtnTxt = '打开'
    }
  },
  clickAdBtn(evt) {
    evt.stopPropagation()
    // 判断是否带下载按钮
    let createType = this.adData.creativeType
    let ans = this.isDownloadAd(createType)
    console.info('clickAdBtn dealdownload ans=' + ans)
    if (ans) {
      this.dealdownload()
    } else {
      this.reportNativeClick()
    }
  },
  dealdownload: function () {
    let downloadstatus = nativeAd.getAppStatus({
      adId: this.adData.adId,
    })
    console.log('downloadstatus downloadstatus = ' + downloadstatus)
    if (downloadstatus === 'DOWNLOAD') {
      showDialog({
        message: '是否开始下载',
        buttons: [{ text: '确定' }, { text: '取消' }],
        autocancel: false,
        success: ({ index }) => {
          if (index === 0) {
            this.startDl()
          }
        },
      })
    } else if (downloadstatus === 'DOWNLOADING') {
      this.adData.clickBtnTxt = '暂停'
      nativeAd.pauseDownload({
        adId: this.adData.adId,
      })
    } else if (downloadstatus === 'PAUSE') {
      this.adData.clickBtnTxt = '恢复下载'
      nativeAd.resumeDownload({
        adId: this.adData.adId,
      })
    } else {
      this.reportNativeClick()
    }
  },
  startDl: function () {
    //下载未开始，应用初始状态。
    const resultCode = nativeAd.startDownload({
      adId: this.adData.adId,
    })
    console.log('dealdownload startDownload  resultCode= ' + resultCode)
    const progress = nativeAd.getDownloadProgress({
      adId: this.adData.adId,
    })
    console.log('getDownloadProgress progress = ' + progress)
    nativeAd.onDownloadProgress(data => {
      console.log('onDownloadProgress data = ', data)
      this.adData.clickBtnTxt = data.progress + '%'
    })
    nativeAd.onStatusChanged(data => {
      console.log('onStatusChanged data = ', data)
      let status = data.status
      this.listenNativeAdDownloadStatus(status)
    })
  },
  removeAdListen: function () {
    if (nativeAd) {
      nativeAd.offDownloadProgress()
      nativeAd.offError(() => {
        console.log('nativeAd offError')
      })
      nativeAd.offLoad(() => {
        console.log('nativeAd offLoad')
      })
      nativeAd.offStatusChanged()
    }
  },
  onDestroy() {
    try {
      nativeAd && nativeAd.destroy()
    } catch (e) {}
  },
  bdAdShow() {},
  loadHandler() {
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: this.eventName,
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adid,
        ad_type: 6,
        ecpm: 0,
      },
    })
  },
  errorHandler() {
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: this.eventName,
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adid,
      },
    })
    this.native.isShow2 = false
  },
  closeAd(event) {
    event && event.stopPropagation && event.stopPropagation()
    this.native.isShow = false
    this.native.isShow2 = false
  },
  clickAd() {
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: this.eventName,
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adid,
      },
    })
  },
}
</script>
