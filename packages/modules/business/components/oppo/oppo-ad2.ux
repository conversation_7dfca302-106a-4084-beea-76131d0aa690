<template>
  <div>
    <block if="showAd">
      <ad
        adid="{{adData.adId}}"
        close-btn-position="right-top"
        app-info-area-x="0px"
        app-info-area-y="0px"
        app-info-area-lines="1"
        privacy-area-x="530px"
        privacy-area-y="0px"
        @adshow="adShow"
        @adclick="adClick"
        @error="error"
        type="native"
        class="ad-root"
      >
        <ad-clickable-area type="click" class="click-container">
          <div class="container">
            <ad-clickable-area
              class="ad-video"
              type="video"
              if="{{adData.creativeType === 13 || adData.creativeType === 16}}"
            ></ad-clickable-area>
            <block if="{{ !isUseNewVersion }}">
              <div class="ad-clickbtn">
                <text>{{ adData.clickBtnTxt }}</text>
              </div>
            </block>
            <block else>
              <ad-clickable-area
                class="ad-clickbtn"
                type="button"
              ></ad-clickable-area>
            </block>
            <div class="debug" if="{{debugText}}">
              <text>{{ debugText }}</text>
            </div>
            <div class="countdown" if="showSplashAd">
              <text>跳过 {{ countdown }}s</text>
            </div>
          </div>
        </ad-clickable-area>
      </ad>
    </block>
  </div>
</template>

<script>
import device from '@system.device'
import file from '@system.file'
import { trackEvent } from '../../lib'
import adsdk from '../../lib/adsdk'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      countdown: 3,
      intervalTag: 0,
      showAd: false,
      showDebug: debug,
      debugText: null,
      isUseNewVersion: false,
    }
  },
  props: {
    reqid: {
      type: String,
    },
    showSplashAd: {
      type: Boolean,
      default: false,
    },
  },
  async onReady() {
    this.platformVersionCode = await this.getDeviceInfo('platformVersionCode')
    //六要素&关闭按钮&下载按钮是引擎版本1123+支持
    this.isUseNewVersion = this.platformVersionCode >= 1123

    file.readText({
      uri: 'internal://mass/debugtxt',
      success: data => {
        this.showDebug = true
      },
      fail: (data, code) => {},
    })

    await this.loadAd()
  },
  async loadAd() {
    if (this.reqid) {
      this.adObj = adsdk.getAdRequest(this.reqid)?.adObj
    }
    this.adData = this.adObj?.adData
    if (this.adData) {
      if (!this.$valid || !this.$visible) {
        adsdk.adCacheList.push(this.adObj)
        return
      }
      this.$emit('load')
      this.showAd = true
      if (!this.intervalTag && this.showSplashAd) {
        this.intervalTag = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            this.close()
          }
        }, 1000)
      }
      if (this.showDebug) {
        this.debugText = this.adData.title + ' ' + this.adObj.adUnitId
      }
    }
  },
  adShow() {
    this.$emit('show', { ecpm: this.adObj.ecpm })
    adsdk.log(
      `adShow 信息流广告展示成功：${this.adObj.adUnitId} ${this.adObj.ecpm}`
    )
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adData.title + ' ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  error(evt) {
    // this.showAd = false
    this.$emit('error')
    adsdk.log(
      `adError 信息流广告加载出错：${this.adObj.adUnitId} ${evt.errCode} ${evt.errMsg}`
    )
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        err_code: evt.errCode,
        err_msg: evt.errMsg,
      },
    })
  },
  adClick() {
    adsdk.log('ad oppo click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
    if (this.showSplashAd) {
      setTimeout(() => {
        this.close()
      }, 500)
    }
  },
  close() {
    adsdk.log('ad oppo close')
    this.$emit('close')
    clearInterval(this.intervalTag)
  },
  // 获取设备信息
  getDeviceInfo(key) {
    return new Promise((resolve, reject) => {
      device.getInfo({
        success: function (data) {
          if (key) {
            resolve(data[key])
          } else {
            resolve(data)
          }
        },
        fail: function (data, code) {
          reject(`### device.getInfo fail ### ${code}: ${data}`)
        },
      })
    })
  },
}
</script>

<style lang="less">
.ad-root {
  width: 100%;
  height: 100%;
  transform: scale(1.4);
}
.container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  position: relative;
  transform: scale(0.7);

  .ad-video {
    width: 100%;
    height: 100%;
    opacity: 0;
  }

  .ad-clickbtn {
    position: absolute;
    bottom: 64px;
    right: 32px;
    width: 140px;
    height: 56px;
    border-radius: 28px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    opacity: 0;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }

  .debug {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 56px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }

  .countdown {
    position: absolute;
    top: 64px;
    right: 32px;
    width: 160px;
    height: 56px;
    border-radius: 28px;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);

    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }
}
</style>
