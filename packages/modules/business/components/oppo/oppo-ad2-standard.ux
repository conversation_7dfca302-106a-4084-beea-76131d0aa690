<import name="icon" src="@quickapp/apex-ui/components/icon/index"></import>

<template>
  <div>
    <block if="showAd">
      <ad
        adid="{{adData.adId}}"
        class="ad-native"
        close-btn-position="right-top"
        app-info-area-x="0px"
        app-info-area-y="510px"
        app-info-area-lines="1"
        privacy-area-x="0px"
        privacy-area-y="540px"
        @adshow="adShow"
        @adclick="adClick"
        @error="adError(err, evt)"
        type="native"
      >
        <div class="page-container vertical">
          <ad-clickable-area type="click">
            <text class="ad-logo">广告</text>
            <text class="ad-title">{{ adData.title }}</text>
          </ad-clickable-area>
          <div
            class="video-container"
            if="{{adData.creativeType === 13 || adData.creativeType === 16}}"
          >
            <ad-clickable-area
              class="vertical ad-video"
              type="video"
            ></ad-clickable-area>
          </div>
          <ad-clickable-area class="img-div" else>
            <image
              src="{{image}}"
              class="ad-image"
              for="image in adData.imgUrlList"
              tid="image"
            ></image>
            <text
              class="ad-image"
              if="{{!adData.imgUrlList || adData.imgUrlList.length <= 0}}"
            >
              {{ adData.desc }}
            </text>
          </ad-clickable-area>
          <div class="ad-button-container">
            <ad-clickable-area
              class="ad-image-button"
              type="button"
            ></ad-clickable-area>
          </div>
        </div>
      </ad>
    </block>
  </div>
</template>

<script>
import { trackEvent } from '../../lib'
import file from '@system.file'
import adsdk from '../../lib/adsdk'
import VivoAd from '../../lib/ads/vivoAd'
import OppoAd from '../../lib/ads/oppoAd'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      showAd: false,
      showDebug: debug,
      debugText: '',
    }
  },
  props: {
    adid: {
      type: String,
    },
  },
  async onReady() {
    this.adObj = new OppoAd(this.adid)
    await this.adObj.preloadAd()
    this.adData = this.adObj.adData
    if (this.adData) {
      // console.log(`广告数据：${JSON.stringify(this.adData)}`)
      this.showAd = true
    }
  },
  adShow() {
    this.$emit('show', { ecpm: this.adObj.ecpm })
    adsdk.log(
      `adShow 信息流广告展示成功：${this.adObj.adUnitId} ${this.adObj.ecpm}`
    )
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adObj.adUnitId
    }
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  adError(err, evt) {
    // this.showAd = false
    this.$emit('error')
    adsdk.log(
      `adError 信息流广告加载出错：${this.adObj.adUnitId} ${evt.errCode} ${evt.errMsg}`
    )
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
      },
    })
  },
  adClick() {
    console.log('ad int oppo click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  close() {
    console.log('ad int oppo close')
    this.showAd = false
    this.$emit('close')
  },
}
</script>

<style lang="less">
.ad-native {
  background-color: white;
  width: 100%;
  height: 600px;
  padding: 10px;
}
.page-container {
  flex-direction: column;
  margin: 30px 0;
}

.vertical {
  flex-direction: column;
}

.ad-item {
  flex-direction: column;
  padding: 32px 16px;
  justify-content: center;
  align-items: center;
}

.ad-logo {
  background-color: lightgray;
  padding: 4px 8px;
  margin: 0 8px;
  color: #ffffff;
}

.ad-title {
  lines: 1;

  max-width: 500px;
}

.img-div {
  width: 100%;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.ad-image {
  // width/height 此处不可用使用百分比
  width: 100%;
  height: 400px;
  flex: 1;
  margin: 32px 0;
  object-fit: contain;
}

.ad-video {
  width: 100%;
  height: 400px;
  margin: 32px 0;
}

.ad-button-container {
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;

  .ad-image-button {
    width: 140px;
    height: 56px;
    border-radius: 28px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }
}

.video-container {
  flex-direction: column;
}
</style>
