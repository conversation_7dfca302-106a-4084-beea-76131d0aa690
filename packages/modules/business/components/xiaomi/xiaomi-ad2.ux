<template>
  <div>
    <block if="showAd">
      <ad
        adid="{{adData.adId}}"
        closeBtnPosition="right-top"
        @adshow="adShow"
        @adclick="adClick"
        @error="error"
        type="native"
        class="ad-root"
      >
        <ad-clickable-area type="click" class="click-container">
          <div class="container">
            <div class="debug" if="{{debugText}}">
              <text>{{ debugText }}</text>
            </div>
          </div>
        </ad-clickable-area>
      </ad>
    </block>
  </div>
</template>

<script>
import device from '@system.device'
import file from '@system.file'
import { trackEvent } from '../../lib'
import adsdk from '../../lib/adsdk'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      showAd: false,
      showDebug: debug,
      debugText: null,
    }
  },
  props: {
    reqid: {
      type: String,
    },
  },
  async onReady() {
    file.readText({
      uri: 'internal://mass/debugtxt',
      success: data => {
        this.showDebug = true
      },
      fail: (data, code) => {},
    })

    await this.loadAd()
  },
  async loadAd() {
    if (this.reqid) {
      this.adObj = adsdk.getAdRequest(this.reqid)?.adObj
    }
    this.adData = this.adObj?.adData
    if (this.adData) {
      if (!this.$valid || !this.$visible) {
        adsdk.adCacheList.push(this.adObj)
        return
      }
      this.$emit('load')
      this.showAd = true
      if (this.showDebug) {
        this.debugText = 'id: ' + this.adObj.adUnitId
      }
    }
  },
  adShow() {
    this.$emit('show', { ecpm: this.adObj.ecpm })
    adsdk.log(
      `adShow 信息流广告展示成功：${this.adObj.adUnitId} ${this.adObj.ecpm}`
    )
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  error(evt) {
    // this.showAd = false
    this.$emit('error')
    adsdk.log(
      `adError 信息流广告加载出错：${this.adObj.adUnitId} ${evt.errCode} ${evt.errMsg}`
    )
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        err_code: evt.errCode,
        err_msg: evt.errMsg,
      },
    })
  },
  adClick() {
    adsdk.log('ad xiaomi click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  close() {
    adsdk.log('ad xiaomi close')
    this.showAd = false
    this.$emit('close')
  },
  // 获取设备信息
  getDeviceInfo(key) {
    return new Promise((resolve, reject) => {
      device.getInfo({
        success: function (data) {
          if (key) {
            resolve(data[key])
          } else {
            resolve(data)
          }
        },
        fail: function (data, code) {
          reject(`### device.getInfo fail ### ${code}: ${data}`)
        },
      })
    })
  },
}
</script>

<style lang="less">
.ad-root {
  width: 100%;
  height: 100%;
  transform: scale(1.4);
}
.container {
  width: 100%;
  height: 100%;
  flex-direction: column;
  position: relative;
  transform: scale(0.7);

  .debug {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 56px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }
}
</style>
