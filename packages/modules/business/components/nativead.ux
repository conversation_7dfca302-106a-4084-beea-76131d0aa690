<import
  name="honor-ad"
  src="@quickapp/business/components/honor/honor-ad2-standard"
></import>
<import
  name="vivo-ad"
  src="@quickapp/business/components/vivo/vivo-ad2-standard"
></import>
<import
  name="oppo-ad"
  src="@quickapp/business/components/oppo/oppo-ad2-standard"
></import>
<import
  name="huawei-ad"
  src="@quickapp/business/components/huawei/huawei-ad-standard"
></import>

<template>
  <div class="item-container">
    <component is="adComponent" adid="{{adid}}" if="{{adid}}"></component>
  </div>
</template>
<script>
import ad from '@service.ad'
import globalData from '@quickapp/utils/lib/globalData'
import adsdk from '../lib/adsdk'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data: {
    adid: '',
    adComponent: '',
  },
  props: {
    vivoId: {
      type: String,
    },
    honorId: {
      type: String,
    },
    xiaomiId: {
      type: String,
    },
    oppoId: {
      type: String,
    },
    huaweiId: {
      type: String,
    },
  },
  onInit() {
    switch (adsdk.provider()) {
      case 'xiaomi':
        this.adid = this.xiaomiId
        this.adComponent = 'xiaomi-ad'
        break
      case 'oppo':
        this.adid = this.oppoId
        this.adComponent = 'oppo-ad'
        break
      case 'vivo':
        this.adid = this.vivoId
        this.adComponent = 'vivo-ad'
        break
      case 'huawei':
        this.adid = this.huaweiId
        this.adComponent = 'huawei-ad'
        break
      case 'honor':
        this.adid = this.honorId
        this.adComponent = 'honor-ad'
        break
      default:
        this.adComponent = ''
    }
  },
}
</script>

<style></style>
