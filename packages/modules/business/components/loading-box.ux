<script>
export default {
  name: 'loading-box',
  data() {
    return {
      isShow: true,
    }
  },
  onReady() {
    setTimeout(() => {
      this.isShow = false
    }, 3000)
  },
  dismiss() {
    this.isShow = false
  },
}
</script>

<template>
  <div>
    <div class="wrapper" if="isShow">
      <div class="loading-box">
        <image
          class="loading-img"
          src="http://fishing-h5.springdance.cn/images/load.png"
        ></image>
        <text class="loading-text">资源加载中...</text>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.wrapper {
  position: fixed;
  left: 275px;
  top: 700px;
}
.loading-box {
  width: 200px;
  height: 200px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #000000;
  border-radius: 20px;

  .loading-img {
    width: 80px;
    margin-top: 20px;
    animation-name: rotate;
    animation-duration: 800ms;
    animation-fill-mode: none;
    animation-iteration-count: -1;
    animation-timing-function: linear;
  }

  .loading-text {
    font-size: 25px;
    margin-top: 20px;
    color: #ffffff;
  }
}
</style>
