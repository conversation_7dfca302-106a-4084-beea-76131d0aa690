<import name="icon" src="@quickapp/apex-ui/components/icon/index"></import>

<template>
  <div>
    <block if="showAd">
      <ad
        adid="{{adData.adId}}"
        class="ad-native"
        closeBtnPosition="right-top"
        @adshow="adShow"
        @adclick="adClick"
        @error="adError(err, evt)"
        type="native"
      >
        <div class="page-container vertical">
          <ad-clickable-area type="click">
            <text class="ad-title">{{ adData.title }}</text>
          </ad-clickable-area>
          <div
            class="video-container"
            if="{{adData.creativeType === 4 || adData.creativeType === 5}}"
          >
            <ad-clickable-area
              class="vertical ad-video"
              type="video"
            ></ad-clickable-area>
          </div>
          <ad-clickable-area class="img-div" else>
            <image
              src="{{image}}"
              class="ad-image"
              for="image in adData.imgUrlList"
              tid="image"
            ></image>
          </ad-clickable-area>
          <div class="ad-button-container">
            <ad-clickable-area
              class="ad-image-button"
              type="button"
            ></ad-clickable-area>
          </div>
        </div>
      </ad>
    </block>
  </div>
</template>

<script>
import { trackEvent } from '../../lib'
import file from '@system.file'
import adsdk from '../../lib/adsdk'
import VivoAd from '../../lib/ads/vivoAd'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      showAd: false,
      showDebug: debug,
      debugText: '',
    }
  },
  props: {
    adid: {
      type: String,
    },
  },
  async onReady() {
    this.adObj = new VivoAd(this.adid)
    await this.adObj.preloadAd()
    this.adData = this.adObj.adData
    if (this.adData) {
      this.showAd = true
    }
  },
  adShow() {
    this.$emit('show', { ecpm: this.adObj.ecpm })
    adsdk.log(
      `adShow 信息流广告展示成功：${this.adObj.adUnitId} ${this.adObj.ecpm}`
    )
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adObj.adUnitId
    }
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  adError(err, evt) {
    // this.showAd = false
    this.$emit('error')
    adsdk.log(
      `adError 信息流广告加载出错：${this.adObj.adUnitId} ${evt.errCode} ${evt.errMsg}`
    )
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
      },
    })
  },
  adClick() {
    console.log('ad int vivo click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  close() {
    console.log('ad int vivo close')
    this.showAd = false
    this.$emit('close')
  },
}
</script>

<style lang="less">
.ad-native {
  background-color: white;
  width: 100%;
  padding: 10px;
}
.page-container {
  flex-direction: column;
  margin: 60px 0;
}

.vertical {
  flex-direction: column;
}

.ad-item {
  flex-direction: column;
  padding: 32px 16px;
  justify-content: center;
  align-items: center;
}

.ad-logo {
  background-color: lightgray;
  padding: 4px 8px;
  margin: 0 8px;
  color: #ffffff;
}

.ad-title {
  lines: 1;

  max-width: 500px;
}

.img-div {
  width: 100%;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.ad-image {
  // width/height 此处不可用使用百分比
  width: 100%;
  height: 400px;
  flex: 1;
  margin: 32px 0;
  object-fit: contain;
}

.ad-video {
  width: 100%;
  height: 400px;
  margin: 32px 0;
}

.ad-button-container {
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
}

.video-container {
  flex-direction: column;
}
</style>
