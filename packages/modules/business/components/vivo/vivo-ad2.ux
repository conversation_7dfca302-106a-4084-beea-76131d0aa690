<import name="icon" src="@quickapp/apex-ui/components/icon/index"></import>

<template>
  <div>
    <block if="showAd">
      <ad
        adid="{{adData.adId}}"
        class="ad-native"
        closeBtnPosition="right-top"
        @adshow="adShow"
        @adclick="adClick"
        @error="adError(err, evt)"
        type="native"
      >
        <div class="click-container" id="container" @resize="onResize">
          <ad-clickable-area type="click" class="container">
            <block
              if="{{adData.creativeType === 4 || adData.creativeType === 5}}"
            >
              <ad-clickable-area
                class="ad-video"
                type="video"
              ></ad-clickable-area>
            </block>
            <ad-clickable-area class="ad-logo" type="logo"></ad-clickable-area>
            <div class="debug" if="{{debugText}}">
              <text>{{ debugText }}</text>
            </div>
          </ad-clickable-area>
          <div class="ad-clickbtn-container" if="showAdBtn">
            <drawer autoplay="false" indicator="false" loop="false">
              <ad-clickable-area
                class="ad-clickbtn"
                type="button"
              ></ad-clickable-area>
            </drawer>
            <div class="ad-clickbtn-text" style="{{clickTextTop}}"></div>
          </div>
        </div>
      </ad>
    </block>
  </div>
</template>

<script>
import { trackEvent } from '../../lib'
import adsdk from '../../lib/adsdk'
import config from '../../lib/config'
import globalData from '@quickapp/utils/lib/globalData'

const debug = process.env.NODE_ENV !== 'production'

export default {
  data() {
    return {
      adObj: {},
      adData: {},
      showAd: false,
      showDebug: globalData.isDebug,
      debugText: '',
      showAdBtn: false,
      clickTextTop: '',
    }
  },
  props: {
    reqid: {
      type: String,
    },
  },
  onReady() {
    if (this.reqid) {
      this.adObj = adsdk.getAdRequest(this.reqid)?.adObj
      this.adData = this.adObj?.adData
      if (this.adData) {
        if (!this.$valid || !this.$visible) {
          adsdk.adCacheList.push(this.adObj)
          return
        }
        this.$emit('load')
        this.showAdButton()
        this.showAd = true
        if (this.showDebug) {
          this.debugText = this.adData.title + ' ' + this.adObj.adUnitId
        }
      }
    }
  },
  showAdButton() {
    let adBtnPercent = config.getAdConfig()['ad_btn_percent'] || 60
    if (Math.random() * 100 >= adBtnPercent) {
      return
    }
    let adConf = adsdk.getAdRequest(this.reqid)
    this.clickTextTop = 'top:' + (adConf?.height / 2 - 9) + 'px'
    this.showAdBtn = true
  },
  onResize(event) {
    this.clickTextTop = 'top:' + (event.offsetHeight / 2 - 9) + 'px'
  },
  adShow() {
    this.$emit('show', { ecpm: this.adObj.ecpm })
    adsdk.log(
      `adShow 信息流广告展示成功：${this.adObj.adUnitId} ${this.adObj.ecpm}`
    )
    if (this.showDebug) {
      this.debugText = '曝光: ' + this.adData.title + ' ' + this.adObj.adUnitId
    }
    adsdk.onAdShow()
    trackEvent({
      category: 'advertise',
      action: 'exposure',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
        ecpm: this.adObj.ecpm,
      },
    })
  },
  adError(err, evt) {
    // this.showAd = false
    this.$emit('error')
    adsdk.log(
      `adError 信息流广告加载出错：${this.adObj.adUnitId} ${evt.errCode} ${evt.errMsg}`
    )
    trackEvent({
      category: 'advertise',
      action: 'fail',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
      },
    })
  },
  adClick() {
    adsdk.log('ad vivo click')
    this.$emit('adclick')
    trackEvent({
      category: 'advertise',
      action: 'click',
      opt_label: 'native_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adObj.adUnitId,
        ad_type: 6,
      },
    })
  },
  close() {
    adsdk.log('ad vivo close')
    this.showAd = false
    this.$emit('close')
  },
}
</script>

<style lang="less">
@keyframes adout {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.4);
  }
}
@keyframes adinner {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.72);
  }
}
.ad-native {
  background-color: rgba(0, 0, 0, 0);
  animation-name: adout;
  animation-duration: 0ms;
  animation-delay: 0ms;
  transform-origin: 0 0;
}
.click-container {
  position: relative;
  animation-name: adinner;
  animation-duration: 0ms;
  animation-delay: 0ms;
  transform-origin: 0 0;
}
.container {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  flex-direction: column;
  position: relative;

  .ad-video {
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0);
    opacity: 0;
  }

  .ad-logo {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    margin-top: -50px;
  }

  .debug {
    position: absolute;
    bottom: 0;
    right: 0;
    height: 56px;
    align-items: center;
    justify-content: center;
    background-color: #2d40e9;
    text {
      font-size: 28px;
      color: white;
      font-weight: bold;
    }
  }
}

.ad-clickbtn-container {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;

  .ad-clickbtn {
    width: 740px;
    height: 100px;
    background-color: black;
    align-items: center;
    justify-content: center;
    font-size: 8px;
  }

  .ad-clickbtn-text {
    position: absolute;
    left: 335px;
    width: 80px;
    height: 18px;
    background-color: black;
    align-items: center;
    justify-content: center;
  }
}
</style>
