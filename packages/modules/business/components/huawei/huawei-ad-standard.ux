<template>
  <div class="item-container">
    <div if="native.isShow" class="container">
      <div style="margin-bottom: 8px; flex-direction: row; align-items: center">
        <text style="font-weight: bold">广告</text>
        <text style="margin-left: 8px">{{ native.adData.title }}</text>
      </div>
      <video
        id="video"
        if="native.isShowVideo"
        src="{{native.adVideoSrc}}"
        autoplay="true"
        onclick="reportNativeClick()"
        class="ad-video"
      ></video>
      <stack class="stackstyle" onclick="reportNativeClick()">
        <image
          if="native.isShowImg"
          class="img"
          src="{{native.adImgSrc}}"
        ></image>
        <ad-button
          class="adbtn"
          onclick="startButton()"
          valuetype="0"
          adunitid="{{adid}}"
          adid="{{native.adData.adId}}"
        ></ad-button>
      </stack>
      <div style="flex-direction: row; width: 100%">
        <text style="width: 100%">{{ native.adData.source }}</text>
        <image
          class="closeImg"
          src="http://fishing-h5.springdance.cn/images/close.webp"
          onclick="closeAd"
        ></image>
      </div>
    </div>
  </div>
</template>
<style>
.container {
  flex-direction: column;
  margin-top: 20px;
  width: 100%;
  margin-bottom: 50px;
}
.stackstyle {
  width: 100%;
  height: 490px;
}
.img {
  width: 100%;
  object-fit: contain;
}
.closeImg {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}
.alert {
  font-size: 40px;
  margin-top: 20px;
  margin-bottom: 20px;
}
.item-container {
  padding: 20px;
  width: 100%;
  flex-direction: column;
  align-items: center;
  align-content: center;
}
.ad-video {
  object-fit: contain;
  width: 100%;
  height: 415px;
}
.btn {
  height: 80px;
  width: 60%;
  background-color: #00bfff;
  color: #ffffff;
  border-radius: 20px;
  margin-bottom: 20px;
}
.btn:active {
  background-color: #058fbd;
}
.adbtn {
  width: 200px;
  height: 50px;
  color: #ffffff;
  background-color: #00bfff;
  border-radius: 8px;
  position: absolute;
  align-self: flex-end;
  bottom: 20px;
  right: 20px;
}
.adbtn:active {
  background-color: #058fbd;
}
</style>
<script>
import ad from '@service.ad'

let nativeAd
export default {
  data: {
    provider: '',
    native: {
      isShow: false,
      adData: {},
      isShowImg: true,
      isShowVideo: true,
      isShowData: true,
      errStr: '',
      btnTxt: '',
      adImgSrc:
        'https://cs02-pps-drcn.dbankcdn.com/cc/creative/upload/********/b750592e-04be-4132-9971-52494b1e5b43.jpg',
      adVideoSrc: '',
      lastClickTime: 0,
    },
  },
  props: {
    adid: {
      default: 'testb65czjivt9',
    },
  },
  onInit() {
    this.$page.setTitleBar({ text: 'Native Ad' })
  },
  onReady(options) {
    console.info('native ad onReady')
    this.showNativeAd()
  },
  onShow(options) {
    if (this.native.isShow) {
      this.reportNativeShow()
    }
  },
  getAdProvider: function () {
    this.provider = ad.getProvider()
  },
  isDownloadAd(creativeType) {
    let downloadTypes = [103, 106, 107, 108, 101, 102, 110]
    return downloadTypes.includes(creativeType)
  },
  showNativeAd() {
    var that = this
    this.getAdProvider()
    if (this.provider !== 'huawei') {
      console.info('the device  does not support ad.')
      return
    }
    nativeAd = ad.createNativeAd({ adUnitId: this.adid })
    nativeAd.onLoad(data => {
      console.info('ad data loaded: ' + JSON.stringify(data))
      this.native.adData = data.adList[0]
      if (this.native.adData) {
        if (this.native.adData.imgUrlList) {
          this.native.adImgSrc = this.native.adData.imgUrlList[0]
          console.info(' this.native.adImgSrc =' + this.native.adImgSrc)
          this.native.isShowImg = true
        } else {
          this.native.isShowImg = false
          this.native.adImgSrc = ''
        }
        if (this.native.adData.clickBtnTxt) {
          this.native.btnTxt = this.native.adData.clickBtnTxt
        } else {
          this.native.btnTxt = ''
        }
        if (
          this.native.adData.videoUrlList &&
          this.native.adData.videoUrlList[0]
        ) {
          this.native.adVideoSrc = this.native.adData.videoUrlList[0]
          this.native.isShowVideo = true
          this.native.isShowImg = false
          this.native.adImgSrc = ''
        } else {
          this.native.isShowVideo = false
          this.native.adVideoSrc = ''
        }
        this.native.isShow = true
        this.native.errStr = ''
        this.reportNativeShow()
      }
    })
    nativeAd.onError(e => {
      console.error('load ad error:' + this.adid + ' ' + JSON.stringify(e))
      this.native.isShowImg = false
      this.native.isShowVideo = false
      this.native.isShow = false
      this.native.errStr = JSON.stringify(e)
    })
    nativeAd.load()
  },
  reportNativeShow() {
    if (nativeAd) {
      nativeAd.reportAdShow({ adId: this.native.adData.adId })
    }
  },
  isFastClick() {
    let now = Date.now()
    if (now - this.lastClickTime < 500) {
      return true
    }
    this.lastClickTime = now
    return false
  },
  reportNativeClick() {
    if (this.isFastClick()) return
    nativeAd.reportAdClick({
      adId: this.native.adData.adId,
    })
  },
  listenNativeAdDownloadStatus(downloadstatus) {
    if (downloadstatus === 'INSTALLED') {
      this.native.btnTxt = 'OPEN'
    }
  },
  startButton(event) {
    console.error('start download result is = ', event.resultCode)
  },
  removeAdListen: function () {
    if (nativeAd) {
      nativeAd.offDownloadProgress()
      nativeAd.offError(() => {
        console.log('nativeAd offError')
      })
      nativeAd.offLoad(() => {
        console.log('nativeAd offLoad')
      })
      nativeAd.offStatusChanged()
    }
  },
  onDestroy() {
    if (nativeAd) {
      nativeAd.destroy()
    }
  },
  closeAd: function () {
    this.native.isShow = false
  },
}
</script>
