import crypto from './crypto'
import fetchS from '@system.fetch'
import storage from '@system.storage'
import adsdk from './adsdk'
import device from './device'
import globalData from '@quickapp/utils/lib/globalData'
import { REGISTER_API_HOST } from './config'
import { StateFlow } from '@quickapp/utils/lib/StateFlow'

// 默认 埋点 api 和 注册 api 是同一个

// 注册URL
const REGISTER_URL = '/client/device'

// app信息，__MANIFEST__ 为项目中注入的信息
const appInfo = {
  id: __MANIFEST__.id,
  package_name: __MANIFEST__.package,
  version_name: __MANIFEST__.versionName,
  version_code: __MANIFEST__.versionCode,
}

export function getClientInfo(deviceInfo, click_id) {
  let device = {
    imei: deviceInfo.imei ? deviceInfo.imei.slice(0, 15) : null,
    oaid: deviceInfo.oaid ? deviceInfo.oaid : null,
    android_id: deviceInfo.androidid ? deviceInfo.androidid : null,
    brand: deviceInfo.brand,
    manufacturer: deviceInfo.manufacturer,
    model: deviceInfo.model,
    click_id: click_id,
    account_id: globalData.accountId ? globalData.accountId : null,
  }
  let os = {
    huawei_os_type:
      deviceInfo.vendorOsName === 'harmonyOS' ? 'Harmony' : 'Android',
    release_version: deviceInfo.osVersionName,
    sdk_version_int: deviceInfo.osVersionCode,
    platform_version: deviceInfo.platformVersionCode,
  }
  let market = {
    channel_code: 'quickapp',
  }
  return {
    device: device,
    os: os,
    ad: {
      mediation_type: 1,
    },
    app: appInfo,
    market: market,
    location: {},
    risk: {},
  }
}

export async function register(click_id) {
  const deviceInfo = await device.getDeviceInfo()
  return new Promise((resolve, reject) => {
    let client = getClientInfo(deviceInfo, click_id)
    globalData.clientInfoCrypt = crypto.aes(JSON.stringify(client))

    fetch({
      method: 'POST',
      url: REGISTER_API_HOST + REGISTER_URL,
      data: client,
    })
      .then(res => {
        if (res.code && res.code !== 200) {
          resolve(false)
          return
        }
        let marketCode = res.market_code
        deviceInfo.udid = res.udid
        deviceInfo.market_code = marketCode
        deviceInfo.accountId = res.account_id
        deviceInfo.planId = res.plan_id
        deviceInfo.groupId = res.group_id
        deviceInfo.creativeId = res.creative_id
        client.device.bucket_id = res.bucket_id
        client.device.device_id = res.udid
        client.risk.level = res.risk_level
        client.risk.advice = res.risk_advice
        client.risk.score = res.risk_score
        client.risk.tags = res.risk_tags
        client.market.market_code = marketCode
        globalData.marketCode = marketCode
        client.app.activation_time = res.activation_time
        if (!globalData.marketCode) {
          adsdk.log('Banned: marketCode empty')
        }
        globalData.clientInfoCrypt = crypto.aes(JSON.stringify(client))
        storage.set({
          key: 'clientInfo',
          value: globalData.clientInfoCrypt,
        })
        storage.set({
          key: 'deviceInfo',
          value: JSON.stringify(deviceInfo),
        })
        globalData.$registered = true
        trackEvent({
          category: 'device',
          action: 'reg',
        })
        if (globalData.eventCache && globalData.eventCache.length > 0) {
          for (let param of globalData.eventCache) {
            trackEvent(param)
          }
          globalData.eventCache = []
        }
        resolve(true)
      })
      .finally(() => {
        resolve(false)
      })
  })
}

/**
 * 通用事件上报
 *
 * @param param 接口入参
 * category      监控的目标的类型
 * action        用户跟目标交互的行为
 * opt_label     事件的一些额外信息
 * opt_value     事件的一些数值信息 int
 * opt_extra     事件的一些附加数据
 */
export function trackEvent(param) {
  setTimeout(async () => {
    if (!globalData.$registered) {
      if (!globalData.eventCache) {
        globalData.eventCache = []
      }
      globalData.eventCache.push(param)
      return
    }
    let deviceInfo = await device.getDeviceInfo()
    param.opt_extra = {
      event_time: Date.now(),
      account_id: deviceInfo.accountId || globalData.accountId,
      plan_id: deviceInfo.planId,
      group_id: deviceInfo.groupId,
      creative_id: deviceInfo.creativeId,
      ...param.opt_extra,
    }
    fetch({
      method: 'POST',
      url: REGISTER_API_HOST + '/track/event',
      data: param,
    })
      .then(r => {
        if (r && r.ok) {
          adsdk.log(`trackEvent ${JSON.stringify(param)}`)
        } else {
          adsdk.log(`trackEvent error ${JSON.stringify(r)}`)
        }
      })
      .catch(e => {
        adsdk.log(`trackEvent error ${JSON.stringify(e)}`)
      })
  })
}

export function fetch(
  { url, data = null, method = 'GET', header = {} },
  debug = false
) {
  let headerOpt = {
    'Client-Info': globalData.clientInfoCrypt,
    'User-Agent': 'quickapp',
    'Content-Type': 'application/json; charset=UTF-8',
    ...header,
  }
  if (!debug) {
    headerOpt.Protocol = 'MCTLS/1.0'
    let time = Date.now().toString()
    let nonce = crypto.createNonceStr()
    headerOpt['Request-Param'] = JSON.stringify([time, nonce])
    headerOpt['Request-Signature'] = crypto.createSign([time, nonce])
  }

  if (method.toLocaleUpperCase() === 'POST') {
    if (!data) {
      data = {}
    }
    data = debug ? JSON.stringify(data) : crypto.aes(JSON.stringify(data))
  }

  // console.log('header', JSON.stringify(headerOpt))

  return new Promise((resolve, reject) => {
    // console.log('trackEvent fetch', url)
    fetchS.fetch({
      url: url,
      data: data,
      header: headerOpt,
      method: method,
      success: res => {
        try {
          if (debug) {
            const result = JSON.parse(res.data)
            if (result.data) {
              resolve(result.data)
            } else {
              adsdk.log(`fetch fail ${url} ${JSON.stringify(result)}`)
              reject(result)
            }
          } else {
            let deStr = crypto.aesDecrypt(JSON.parse(res.data))
            const result = JSON.parse(deStr)
            if (result.data) {
              resolve(result.data)
            } else {
              reject(result)
            }
          }
        } catch (e) {
          reject(res)
        }
      },
      fail: (data, code) => {
        adsdk.log('__fetch_fail__', code, url, JSON.stringify(data))
        resolve({ data, code })
      },
    })
  })
}

/**
 * app.ux onCreate 中调用
 */
export async function createEvt($app) {
  globalData.deviceInfo = await device.getDeviceInfo()
  adsdk.onCreate($app)
}

/**
 * app.ux onShow 中调用
 */
export function showEvt() {
  trackEvent({
    category: 'app',
    action: 'start',
    opt_label: 'app_show',
  })
}

/**
 * app.ux onHide 中调用
 */
export function hideEvt() {
  trackEvent({
    category: 'app',
    action: 'stop',
    opt_label: 'app_show',
  })
}

/**
 * app.ux onDestroy 中调用
 */
export function destroyEvt() {}

/**
 * 混入app
 */
export function mixinsApp(opt) {
  globalData.appStateFlow = new StateFlow('')

  const isAgree = () => {
    return new Promise(resolve => {
      storage.get({
        key: '__is-agree__',
        success: data => {
          if (data) {
            resolve()
          }
        },
      })
    })
  }

  return {
    ...opt,
    onCreate() {
      globalData.appVisible = true
      createEvt(this)
      opt.onCreate && opt.onCreate()
    },
    onShow() {
      isAgree().then(() => {
        showEvt()
      })
      globalData.appVisible = true
      globalData.appStateFlow.value = 'appShow'
      opt.onShow && opt.onShow()
    },
    onHide() {
      isAgree().then(() => {
        hideEvt()
      })
      globalData.appVisible = false
      globalData.appStateFlow.value = 'appHide'
      opt.onHide && opt.onHide()
    },
    onDestroy() {
      isAgree().then(() => {
        destroyEvt()
      })
      opt.onDestroy && opt.onDestroy()
    },
    onError({ message, stack }) {
      console.error('app error', message, stack)
    },
  }
}
