import config from '../config'
import OppoRewardedAd from './oppoRewardedAd'
import VivoRewardedAd from './vivoRewardedAd'
import XiaomiRewardedAd from './xiaomiRewardAd'
import HonorRewardedAd from './honorRewardAd'
import globalData from '@quickapp/utils/lib/globalData'
import adsdk from '../adsdk'

if (!globalData.autoCount) {
  globalData.autoCount = 0
  globalData.clickAdCount = 0
}

function createRewardedAd(adUnitId) {
  switch (adsdk.provider()) {
    case 'vivo':
      return new VivoRewardedAd(adUnitId)
    case 'xiaomi':
      return new XiaomiRewardedAd(adUnitId)
    case 'honor':
    case 'huawei':
      return new HonorRewardedAd(adUnitId)
    default:
      return new OppoRewardedAd(adUnitId)
  }
}

function rewardedAdConfig(context) {
  let adUnitIdList = config.getAdConfig()['reward_all'] || []
  // let ylhAdUnitId = config.getConfig().ad?.ylh?.['reward_ad']
  let officialAd
  let officialAd2
  try {
    officialAd = createRewardedAd(adUnitIdList[0])
  } catch (e) {}
  try {
    officialAd2 = createRewardedAd(adUnitIdList[1])
  } catch (e) {}
  return {
    adUnitIdList,
    rewardedCache: null,
    rewardedAdMap: {},
    loadResolve: null,
    officialAd,
    officialAd2,
    // ylhAd: new YlhRewardedAd(context, ylhAdUnitId),
    onShow() {
      this.checkShow()
    },
    onBackPress() {
      return true
    },
    checkShow() {
      if (!this.officialAd) return
      let rewardedConfig = config.getAdConfig()['rewarded_config']
      if (!rewardedConfig) return
      if (
        globalData.autoCount < rewardedConfig.max &&
        this.adUnitIdList &&
        this.adUnitIdList.length > 0
      ) {
        setTimeout(() => {
          let minEcpm = rewardedConfig.min_ecpm || 0
          this.biddingRank([this.officialAd /*, this.ylhAd*/], minEcpm).then(
            adObj => {
              if (adObj) {
                adObj.show()
                globalData.autoCount++
              }
            }
          )
        }, rewardedConfig.auto_time_sec * 1000)
      }
      if (
        (!this.rewardedCache || !this.rewardedCache.valid) &&
        globalData.clickAdCount < rewardedConfig.clickad_max &&
        this.adUnitIdList &&
        this.adUnitIdList.length > 1
      ) {
        setTimeout(() => {
          let minEcpm = rewardedConfig.clickad_min_ecpm || 0
          this.biddingRank([this.officialAd2], minEcpm).then(adObj => {
            if (adObj) {
              this.rewardedCache = adObj
            }
          })
        })
      }
    },
    showRewardedCache() {
      if (this.rewardedCache) {
        globalData.clickAdCount++
        this.rewardedCache.show()
      }
    },
    async biddingRank(adArray, minEcpm) {
      let rewardedAd = await Promise.all(
        adArray.map(async item => {
          await item.load()
          return item
        })
      ).then(res => res.filter(adObj => adObj.isValid()).sort(this.sortByEcpm))
      if (!rewardedAd || rewardedAd.length <= 0) return null
      if (minEcpm > 0 && rewardedAd[0].ecpm < minEcpm) {
        rewardedAd.forEach(adObj => {
          adObj.sendLossNotification(minEcpm)
        })
        adsdk.log(
          `rewarded ad loss ${rewardedAd[0].adUnitId} ${rewardedAd[0].ecpm} ${minEcpm}`
        )
        return null
      }
      let winRewarded = rewardedAd[0]
      winRewarded.sendWinNotification(rewardedAd[1]?.ecpm)
      let lossRewarded = rewardedAd.slice(1)
      lossRewarded.forEach(adObj => {
        adObj.sendLossNotification(winRewarded.ecpm)
      })
      adsdk.log(`rewarded ad win ${winRewarded.adUnitId} ${winRewarded.ecpm}`)
      return winRewarded
    },
    sortByEcpm(a, b) {
      return b.ecpm - a.ecpm
    },
  }
}

export default rewardedAdConfig
