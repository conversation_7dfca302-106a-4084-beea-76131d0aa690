import ad from '@service.ad'
import { trackEvent } from '../index'
import adsdk from '../adsdk'

class VivoRewardedAd {
  rewardVideoAdMultiton = null
  rewardedAd = null
  ecpm = -1

  constructor(adUnitId) {
    this.adUnitId = adUnitId
    this.initRewardedVideo()
  }

  initRewardedVideo() {
    this.rewardVideoAdMultiton = ad.createRewardedVideoAd({
      multiton: true,
    })
  }

  load() {
    return new Promise((resolve, reject) => {
      if (!this.adUnitId) {
        resolve(false)
        return
      }
      let adParams = {
        adUnitId: this.adUnitId,
        type: 'rewardedVideoAd',
        channel: 'quickapp',
      }
      ad.preloadAd({
        ...adParams,
        success: data => {
          let list = Array.isArray(data)
            ? data
            : typeof data === 'object'
            ? [data]
            : []
          this.loadSuccess(list[0])
          resolve(true)
        },
        fail: (data, code) => {
          if (code === 205) {
            // 错误码205时，表示缓存已满导致加载失败，此时从缓存返回3个广告
            this.loadSuccess(data.adList[0])
            resolve(true)
          } else {
            this.rewardedAd = null
            resolve(false)
            trackEvent({
              category: 'advertise',
              action: 'fail',
              opt_label: 'reward_ad',
              opt_extra: {
                event_time: Date.now(),
                unit_id: this.adUnitId,
              },
            })
            adsdk.log(`fail!data= ${JSON.stringify(data)}, code= ${code}`)
          }
        },
      })
      trackEvent({
        category: 'advertise',
        action: 'request',
        opt_label: 'reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adUnitId,
        },
      })
      adsdk.log('激励视频广告初始化')
    })
  }

  show() {
    if (!this.rewardedAd) return
    this.rewardVideoAdMultiton.onClick(() => {
      trackEvent({
        category: 'advertise',
        action: 'click',
        opt_label: 'reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.rewardedAd.adid,
        },
      })
    })
    this.rewardVideoAdMultiton
      .show({
        adid: this.rewardedAd.adid,
      })
      .then(res => {
        adsdk.log('调用激励视频广告展示 ' + res)
        trackEvent({
          category: 'advertise',
          action: 'exposure',
          opt_label: 'reward_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.rewardedAd.adid,
            ecpm: this.rewardedAd.price,
          },
        })
      })
  }

  loadSuccess(rewardedAd) {
    this.rewardedAd = rewardedAd
    this.ecpm = rewardedAd.price
    trackEvent({
      category: 'advertise',
      action: 'fill',
      opt_label: 'reward_ad',
      opt_extra: {
        event_time: Date.now(),
        unit_id: this.adUnitId,
        ecpm: rewardedAd.price,
      },
    })
  }

  isValid() {
    return !!this.rewardedAd
  }

  sendWinNotification() {
    adsdk.log('通知竞价成功接口')
    const data = this.rewardVideoAdMultiton.sendWinNotification({
      adid: this.rewardedAd.adid,
      price: this.rewardedAd.price,
    })
    adsdk.log(`通知竞价成功接口正常返回:${JSON.stringify(data)}`)
  }
  sendLossNotification(winPrice) {
    const data = this.rewardVideoAdMultiton.sendLossNotification({
      adid: this.rewardedAd.adid,
      price: winPrice,
      reason: 1,
      isDelete: true,
    })

    adsdk.log(`通知竞价失败接口正常返回:${JSON.stringify(data)}`)
  }
}

export default VivoRewardedAd
