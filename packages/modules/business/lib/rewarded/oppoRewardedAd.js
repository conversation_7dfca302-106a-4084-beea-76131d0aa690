import { trackEvent } from '../index'
import ad from '@service.ad'
import adsdk from '../adsdk'

class OppoRewardedAd {
  loadResolve = null
  rewardedVideoAd = null
  ecpm = -1
  valid = false

  constructor(adUnitId) {
    this.adUnitId = adUnitId
    this.initRewardedVideo()
  }

  initRewardedVideo() {
    if (!this.adUnitId) return
    let rewardedVideoAd = ad.createRewardedVideoAd({
      adUnitId: this.adUnitId,
      allowRecommend: true,
    })

    rewardedVideoAd.onError(err => {
      const str = `激励视频广告错误: ${err.errMsg}, ${err.errCode}`
      adsdk.log(str)
      trackEvent({
        category: 'advertise',
        action: 'fail',
        opt_label: 'reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adUnitId,
        },
      })
      this.valid = false
      this.loadResolve?.(false)
    })
    try {
      rewardedVideoAd.onClick(() => {
        const str = '激励视频广告 click 事件'
        adsdk.log(str)
        trackEvent({
          category: 'advertise',
          action: 'click',
          opt_label: 'reward_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.adUnitId,
          },
        })
      })
    } catch (e) {}
    rewardedVideoAd.onClose(() => {
      adsdk.log(`rewardedVideoAd close ${this.adUnitId}`)
    })
    rewardedVideoAd.onLoad(() => {
      let ecpm = -1
      try {
        ecpm = rewardedVideoAd.getECPM().ecpm
      } catch (e) {}
      this.ecpm = ecpm
      trackEvent({
        category: 'advertise',
        action: 'fill',
        opt_label: 'reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adUnitId,
          ecpm: ecpm,
        },
      })
      this.valid = true
      this.loadResolve?.(true)
    })
    this.rewardedVideoAd = rewardedVideoAd
  }

  load() {
    return new Promise((resolve, reject) => {
      if (!this.adUnitId) {
        resolve(false)
        return
      }
      trackEvent({
        category: 'advertise',
        action: 'request',
        opt_label: 'reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adUnitId,
        },
      })
      this.loadResolve = resolve
      this.rewardedVideoAd.load()
    })
  }

  show() {
    this.rewardedVideoAd?.show().then(res => {
      adsdk.log('调用激励视频广告展示 ' + res)
      trackEvent({
        category: 'advertise',
        action: 'exposure',
        opt_label: 'reward_ad',
        opt_extra: {
          event_time: Date.now(),
          unit_id: this.adUnitId,
          ecpm: this.ecpm,
        },
      })
    })
    this.valid = false
  }

  isValid() {
    return this.valid
  }

  sendWinNotification(lossPrice) {
    adsdk.log('通知竞价成功')
    const data = this.rewardedVideoAd.notifyRankWin({
      lossPrice: lossPrice,
    })
    adsdk.log(`通知竞价成功接口正常返回:${JSON.stringify(data)}`)
  }

  sendLossNotification(winPrice) {
    const data = this.rewardedVideoAd.notifyRankLoss({
      winPrice: winPrice,
      code: 1, //可传 1,2,3,4
      channel: 'other', //可传mob,other,其他自定义值
    })

    adsdk.log(`通知竞价失败接口正常返回:${JSON.stringify(data)}`)
  }
}

export default OppoRewardedAd
