import CryptoJS from 'crypto-js'

const AES_KEY = 'WfxSbBvqxpHlMMoq'
const SIGN_KEY = '803ddd07b7374318b83cdc2bf8509e81'

function base64(str) {
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(str))
}

/**
 * 解析base 64
 * @param bStr
 * @returns {*}
 */
export function dncBase64(bStr) {
  return CryptoJS.enc.Base64.parse(bStr)
}

function aes(str) {
  let key = CryptoJS.enc.Utf8.parse(AES_KEY)
  return CryptoJS.AES.encrypt(str, key, {
    iv: key,
  }).toString()
}

function aesDecrypt(str) {
  let key = CryptoJS.enc.Utf8.parse(AES_KEY)
  let bStr = CryptoJS.AES.decrypt(str, key, {
    iv: key,
  }).toString(CryptoJS.enc.Base64)
  return CryptoJS.enc.Base64.parse(bStr).toString(CryptoJS.enc.Utf8)
}

function xor(text, key) {
  text = CryptoJS.enc.Base64.parse(text).toString(CryptoJS.enc.Utf8)
  let result = ''
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i)
    const keyCharCode = key.charCodeAt(i % key.length)
    const xorCharCode = charCode ^ keyCharCode
    result += String.fromCharCode(xorCharCode)
  }
  return result
}

function createNonceStr() {
  let chars = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
  ]
  let nums = ''
  for (let i = 0; i < 16; i++) {
    let id = parseInt(Math.random() * 61)
    nums += chars[id]
  }
  return nums
}

function createSign(p) {
  p = [SIGN_KEY, ...p].sort().join('')
  return CryptoJS.SHA1(p).toString(CryptoJS.enc.Hex)
}

export default {
  base64,
  dncBase64,
  aes,
  aesDecrypt,
  createNonceStr,
  createSign,
  xor,
}
