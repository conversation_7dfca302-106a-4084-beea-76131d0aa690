/**
 * 获取设备信息
 * @returns deviceInfo: {
 * brand	String	设备品牌
 * manufacturer	String	设备生产商
 * model	String	设备型号
 * product	String	设备代号
 * osType	String	操作系统名称
 * osVersionName	String	操作系统版本名称
 * osVersionCode	Integer	操作系统版本号
 * platformVersionName	String	运行平台版本名称
 * platformVersionCode	Integer	运行平台版本号
 * language	String	系统语言
 * region	String	系统地区
 * screenWidth	Integer	屏幕宽
 * screenHeight	Integer	屏幕高
 * windowWidth 1030+	Integer	可使用窗口宽度
 * windowHeight 1030+	Integer	可使用窗口高度
 * statusBarHeight 1030+	Integer	状态栏高度
 * screenDensity 1040+	Float	设备的屏幕密度
 * vendorOsName 1080+	String	手机厂商系统的名称，如 ColorOS
 * vendorOsVersion 1080+	String	手机厂商系统的版本号
 * cutout 1080+	Array	针对异形屏(比如刘海屏、水滴屏和开孔屏)返回异形区域的位置大小。Array 中每个 item 表示一个异形区域的描述。item 参数：
 * left:cutout 左边界距离屏幕左边距离
 * top:cutout 上边界距离屏幕上边距离
 * right:cutout 右边界距离屏幕右边距离
 * bottom:cutout 下边界距离屏幕下边距离
 * cutout 的坐标描述以竖屏为基准。即在横屏和竖屏下获取的 cutout 参数描述都是一样的。
 * deviceType 1090+	String	当前快应用引擎的设备类型，手机版为'phone'，电视为'tv'，平板为'tablet'，折叠屏为'foldable'
 * screenRefreshRate 1100+	Float	获取屏幕显示刷新率(获取帧率可能不为60, 90, 144等标准帧率)
 *
 * oaid
 * imei 设备唯一标识。在 Android 上返回 IMEI 或 MEID; 在 Android Q 之后，除了华为手机返回 aaid(应用匿名设备标识符)，其他厂商手机如果支持 oaid（匿名设备标识符）则返回 oaid，否则返回空值。
 * androidid
 * udid 注册接口成功以后赋值
 * market_code 注册接口成功以后赋值
 * is_auto_agree 自动同意
 * }
 */
import config from './config'
import crypto from './crypto'
import app from '@system.app'
import storage from '@system.storage'
import pkg from '@system.package'
import adsdk from './adsdk'
import globalData from '@quickapp/utils/lib/globalData'
import { trackEvent } from './index'

const debug = process.env.NODE_ENV !== 'production'

function getDeviceInfo() {
  return new Promise((resolve, reject) => {
    const device = require('@system.device')

    storage.get({
      key: 'deviceInfo',
      success: data => {
        if (data) {
          data = JSON.parse(data, (key, value) => {
            return value === 'null' ? null : value
          })
          resolve(data)
        } else {
          let info = {}
          let count = 0
          device.getInfo({
            success: data => {
              info = {
                ...info,
                ...data,
              }
              count++
              if (count >= 3) {
                saveInfo(resolve, info)
              }
            },
            fail: () => {
              count++
              if (count >= 3) {
                saveInfo(resolve, info)
              }
            },
          })
          device.getOAID({
            success: data => {
              console.log(`oaid: ${data.oaid}`)
              info = {
                ...info,
                oaid: data.oaid,
              }
              count++
              if (count >= 3) {
                saveInfo(resolve, info)
              }
            },
            fail: () => {
              count++
              if (count >= 3) {
                saveInfo(resolve, info)
              }
            },
          })
          device.getUserId({
            // type: ['user'],
            success: data => {
              info = {
                ...info,
                // imei: data.device,
                androidid: data.userId,
              }
              count++
              if (count >= 3) {
                saveInfo(resolve, info)
              }
            },
            fail: () => {
              count++
              if (count >= 3) {
                saveInfo(resolve, info)
              }
            },
          })
        }
      },
      fail: () => {
        console.log('trackEvent __getDeviceInfo_fail__')
        resolve({})
      },
    })
  })
}

function parseClickId(clickId, clickType) {
  if (!clickId) {
    adsdk.log(`Banned: clickId empty`)
    return false
  }
  if (clickType === 'oneJump') {
    return true
  }
  clickId = decodeURIComponent(clickId)
  let id = __MANIFEST__.id
  let packageName = __MANIFEST__.package
  try {
    let xorKey = packageName.replace(/\./g, ',')
    let info = JSON.parse(crypto.xor(clickId, xorKey))
    if (info.realClickId) {
      globalData.realClickId = info.realClickId
    }
    if (info.id == id && Date.now() - info.time < 600 * 1000) {
      return true
    }
    adsdk.log(
      `Banned: invalid clickId ${info.id} ${id} ${
        info.packageName
      } ${packageName} ${new Date(info.time).toLocaleString()}`
    )
  } catch (e) {
    adsdk.log(`Banned: invalid clickId ${e}`)
  }
  return false
}

async function isBanned() {
  let info = app.getInfo()
  trackEvent({
    category: 'device',
    action: 'source',
    opt_extra: {
      ...info.source,
    },
  })
  let blackApps = config.getConfig()['black_apps'] || []
  for (const app of blackApps) {
    let installed = await hasInstalled(app)
    if (installed) {
      adsdk.log(`Banned: installed black app ${app}`)
      return true
    }
  }
  let blackSourceApps = config.getConfig()['black_source_apps'] || []
  if (blackSourceApps.includes(info.source.packageName)) {
    adsdk.log(`Banned: installed from black source ${info.source.packageName}`)
    return true
  }
  return false
}

function hasInstalled(packageName) {
  return new Promise((resolve, reject) => {
    pkg.hasInstalled({
      package: packageName,
      success: data => {
        resolve(data.result)
      },
      fail: (data, code) => {
        resolve(false)
      },
    })
  })
}

function saveInfo(resolve, info) {
  const storage = require('@system.storage')
  storage.set({
    key: 'deviceInfo',
    value: JSON.stringify(info),
  })

  resolve(info)
}

export default {
  getDeviceInfo,
  parseClickId,
  isBanned,
}
