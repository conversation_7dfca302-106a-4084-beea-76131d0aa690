import fetchS from '@system.fetch'
import adsdk from './adsdk'
import globalData from '@quickapp/utils/lib/globalData'
import { createRequest } from '@quickapp/utils'

const debug = process.env.NODE_ENV !== 'production'
const CONFIG_API_HOST = 'http://mad-api.springdance.cn'
const CODE = __MANIFEST__.configCode

const API_HOST = 'http://quick-app-api.springdance.cn/quick-app'
const WEATHER_API_HOST = 'http://weather-api.springdance.cn'
export const REGISTER_API_HOST = 'https://warehouse-api.springdance.cn'

export const request = createRequest(API_HOST)
export const weatherRequest = createRequest(WEATHER_API_HOST)

function getConfig() {
  if (!globalData.$config) {
    globalData.$config = {}
  }
  return globalData.$config
}

function getAdConfig() {
  let provider = adsdk.provider()
  let config = getConfig().ad?.[provider] || globalData.ad?.[provider] || {}
  if (globalData.channel) {
    config = {
      ...config,
      ...getConfig().ad?.[globalData.channel],
    }
  }
  return config
}

function updateConfig(retry = false) {
  return new Promise((resolve, reject) => {
    let url = `${CONFIG_API_HOST}/config/by_code/${CODE}`
    fetchS.fetch({
      url: url,
      method: 'GET',
      success: res => {
        const result = JSON.parse(res.data)
        if (result.data && result.data.ad) {
          globalData.$config = result.data
          resolve(globalData.$config)
        } else {
          adsdk.log('updateConfig error ' + JSON.stringify(res))
          updateConfigRetry(retry, resolve)
        }
      },
      fail: (data, code) => {
        adsdk.log('updateConfig error ' + JSON.stringify(data))
        updateConfigRetry(retry, resolve)
      },
      complete: () => {},
    })
  })
}

function updateConfigRetry(retry, resolve) {
  if (!retry) {
    updateConfig(true).finally(() => {
      resolve(globalData.$config)
    })
  } else {
    resolve(globalData.$config)
  }
}

export default {
  getConfig,
  getAdConfig,
  updateConfig,
}
