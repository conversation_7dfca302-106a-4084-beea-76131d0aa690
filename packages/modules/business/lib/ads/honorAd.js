import ad from '@service.ad'
import adsdk from '../adsdk'
import { trackEvent } from '../index'
import config from '../config'

class HonorAd {
  adData = null
  ecpm = 0
  ecpm_floor = 0
  type = 'standard'

  constructor(adUnitId, type, ecpm_floor) {
    this.adUnitId = adUnitId
    this.type = type
    this.ecpm_floor = ecpm_floor
  }

  preloadAd() {
    return new Promise((resolve, reject) => {
      ad.preloadAd({
        adUnitId: this.adUnitId,
        allowRecommend: true,
        adCount: 1,
        type: 'native',
        success: res => {
          if (res.resultCode === 0) {
            this.adData = res.adInstanceList[0]
            if (this.adData.mediaBid > 0) {
              this.ecpm = this.adData.mediaBid
            } else {
              this.ecpm = this.ecpm_floor
            }
            resolve(this.adData)
          } else {
            adsdk.log(
              `honorAdRequest fail ${this.adUnitId} data: ${JSON.stringify(
                res
              )}`
            )
            try {
              const failReportPercent =
                config.getAdConfig()['fail_report_percent']
              if (
                failReportPercent &&
                Math.random() * 100 < failReportPercent
              ) {
                trackEvent({
                  category: 'advertise',
                  action: 'fail',
                  opt_label: 'native_ad',
                  opt_extra: {
                    unit_id: this.adUnitId,
                    code: res.resultCode,
                    err_msg: res.resultMsg,
                  },
                })
              }
            } catch (e) {}
            resolve(null)
          }
        },
        fail: (data, code) => {
          adsdk.log(
            `honorAdRequest fail ${this.adUnitId} data: ${JSON.stringify(
              data
            )} code: ${code}`
          )
          try {
            const failReportPercent =
              config.getAdConfig()['fail_report_percent']
            if (failReportPercent && Math.random() * 100 < failReportPercent) {
              trackEvent({
                category: 'advertise',
                action: 'fail',
                opt_label: 'native_ad',
                opt_extra: {
                  unit_id: this.adUnitId,
                  code: data.resultCode,
                  err_msg: data.resultMsg,
                },
              })
            }
          } catch (e) {}
          resolve(null)
        },
      })
    })
  }

  isDownloadAd() {
    return this.adData.interactionType === 0
  }

  showBannerAd(style) {
    if (!this.adUnitId) return
    let bannerAd = ad.createBannerAd({
      adUnitId: this.adUnitId,
      style,
      allowRecommend: true,
    })
    bannerAd.onShow(res => {
      if (res === 'success') {
        // console.log(`bannerAd show success ${this.ecpm}`)
        trackEvent({
          category: 'advertise',
          action: 'exposure',
          opt_label: 'banner_ad',
          opt_extra: {
            event_time: Date.now(),
            unit_id: this.adUnitId,
            ad_type: 6,
            ecpm: this.ecpm,
          },
        })
      }
    })
    bannerAd.load().then(res => {
      if (res.data.mediaBid) {
        this.ecpm = res.data.mediaBid
      }
      bannerAd.show()
    })
  }

  sendWinNotification(lossEcpm) {
    let biddingParams = {
      adid: this.adData.adid,
      winPrice: this.adData.mediaBid,
      highestLossPrice: lossEcpm || 0,
    }
    ad.sendWinNotification(biddingParams)
  }

  sendLossNotification(winEcpm) {
    let biddingParams = {
      adid: this.adData.adid,
      winPrice: winEcpm || 0,
      reason: 1, // 竞败原因
      src: 'others',
      winPkg: '',
    }
    ad.sendLossNotification(biddingParams)
  }
}

export default HonorAd
