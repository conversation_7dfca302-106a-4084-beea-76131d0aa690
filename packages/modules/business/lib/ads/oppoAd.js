import ad from '@service.ad'
import adsdk from '../adsdk'
import config from '../config'
import { trackEvent } from '../index'

class OppoAd {
  adUnitId = ''
  adData = null
  ecpm = 0
  ecpm_floor = 0
  type = 'standard'
  timeout = 3000

  constructor(adUnitId, type = 'standard', ecpm_floor = 0) {
    this.adUnitId = adUnitId
    this.type = type
    this.ecpm_floor = ecpm_floor
  }

  preloadAd() {
    return new Promise((resolve, reject) => {
      try {
        // adsdk.log(`oppoAdRequest preloadAd ${this.adUnitId} ${Date.now()}`)
        ad.preloadAd({
          adUnitId: this.adUnitId, // 原生信息流广告广告位id
          adCount: 1,
          ecpm: true,
          type: 'native', // 原生信息流广告
          success: data => {
            // adsdk.log(`oppoAdRequest success ${this.adUnitId} ${Date.now()}`)
            this.adData = data.adList[0]
            if (this.adData) {
              if (this.adData.ecpm > 0) {
                this.ecpm = this.adData.ecpm
              } else {
                this.ecpm = this.ecpm_floor
              }
              this.adData.unitId = this.adUnitId
              resolve(this.adData)
            } else {
              resolve(null)
            }
          },
          fail: (data, code) => {
            adsdk.log(
              `oppoAdRequest fail ${this.adUnitId} data: ${JSON.stringify(
                data
              )} code: ${code}`
            )
            try {
              const failReportPercent =
                config.getAdConfig()['fail_report_percent']
              if (
                failReportPercent &&
                Math.random() * 100 < failReportPercent
              ) {
                trackEvent({
                  category: 'advertise',
                  action: 'fail',
                  opt_label: 'native_ad',
                  opt_extra: {
                    unit_id: this.adUnitId,
                    code: data.errCode,
                    err_msg: data.errMsg,
                  },
                })
              }
            } catch (e) {}
            resolve(null)
          },
        })
      } catch (e) {
        resolve(null)
      }
    })
  }

  isDownloadAd() {
    return this.adData.contentType === 2
  }

  sendWinNotification(lossEcpm) {
    ad.notifyRankWin({
      adid: this.adData.adId,
      lossPrice: lossEcpm || 0,
    })
  }

  sendLossNotification(winEcpm) {
    ad.notifyRankLoss({
      adid: this.adData.adId,
      winPrice: winEcpm || 0,
      code: 1, //可传 1,2,3,4
      channel: 'mob', //可传mob,other,其他自定义值
    })
  }
}

export default OppoAd
