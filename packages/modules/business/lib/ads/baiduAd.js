import adsdk from '../adsdk'

class BaiduAd {
  adData = null
  ecpm = -1
  type = 'standard'

  constructor(context, appId, adUnitId, type = 'standard') {
    this.context = context
    this.appId = appId
    this.adUnitId = adUnitId
    this.type = type
  }

  preloadAd() {
    return this.context.$app.$def.union_quick_app_sdk
      ?.preloadAd({
        apid: this.adUnitId,
        appid: this.appId,
        type: 'feed',
      })
      .then(data => {
        adsdk.log(`baidu request success ${JSON.stringify(data)}`)
        const { adPrice } = data
        this.adData = data
        this.ecpm = adPrice || -1
      })
      .catch(err => {
        adsdk.log(`baidu request error ${this.appId} ${this.adUnitId} ${err}`)
      })
  }

  sendWinNotification(lossEcpm) {
    return this.context.$app.$def.union_quick_app_sdk?.biddingSuccess({
      apid: this.adUnitId,
      wininfo: {
        ecpm: this.ecpm,
        adn: 10,
        ad_t: 1,
        ad_time: Date.now(),
        bid_t: 3,
      },
    })
  }

  sendLossNotification(winEcpm) {
    return this.context.$app.$def.union_quick_app_sdk?.biddingFail({
      apid: this.adUnitId,
      lossinfo: {
        ecpm: winEcpm,
        adn: 10,
        ad_t: 1,
        ad_time: Date.now(),
        bid_t: 3,
        reason: 203,
        is_s: 1,
        is_c: 2,
      },
    })
  }
}

export default BaiduAd
