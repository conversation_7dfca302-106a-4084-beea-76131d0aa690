import adsdk from '../adsdk'

class YlhAd {
  adData = null
  ecpm = 0
  type = 'standard'

  constructor(context, appId, adUnitId, type = 'standard') {
    this.context = context
    this.appId = appId
    this.adUnitId = adUnitId
    this.type = type
  }

  preloadAd() {
    if (!this.context) return
    return new Promise((resolve, reject) => {
      this.context.$app.$def.ylh_sdk?.onYlhReady(() => {
        const params = {
          count: 1,
          appId: this.appId,
          placementId: this.adUnitId,
          recommendation: 0,
        }
        // 调用 loadFeedAd 加载广告数据
        this.context.$app.$def.ylh_sdk
          ?.loadFeedAd(params)
          .then(res => {
            this.adData = res[0]
            this.ecpm = this.adData.getECPM()
            // adsdk.log(`ylh request success ${this.adUnitId} ${this.ecpm}`)
            resolve(this.adData)
          })
          .catch(err => {
            resolve()
            adsdk.log(`ylh request error ${err}`)
          })
      })
    })
  }

  sendWinNotification(lossEcpm) {
    /*try {
      this.adData
        .sendWinNotification({
          actionPrice: this.ecpm,
          highestLossPrice: lossEcpm,
        })
        .then(result => {
          adsdk.log(`ylh sendWinNotification ${result}`)
        })
        .catch(result => {
          adsdk.log(`ylh sendWinNotification err ${result}`)
        })
    } catch (e) {
      adsdk.log(`ylh sendWinNotification err ${e}`)
    }*/
  }

  sendLossNotification(winEcpm) {
    /*try {
      this.adData
        .sendLossNotification({
          actionPrice: winEcpm,
          actionLoss: this.ecpm,
          actionSeatId: 2,
        })
        .then(result => {
          adsdk.log(`ylh sendLossNotification ${result}`)
        })
        .catch(result => {
          adsdk.log(`ylh sendWinNotification err ${result}`)
        })
    } catch (e) {
      adsdk.log(`ylh sendWinNotification err ${e}`)
    }*/
  }
}

export default YlhAd
