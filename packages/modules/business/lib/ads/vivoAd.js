import ad from '@service.ad'
import { showToast } from '@system.prompt'
import adsdk from '../adsdk'
import config from '../config'
import { trackEvent } from '../index'

class VivoAd {
  adData = null
  ecpm = 0
  type = 'standard'
  ecpm_floor = 0

  constructor(adUnitId, type = 'standard', ecpm_floor = 0) {
    this.adUnitId = adUnitId
    this.type = type
    this.ecpm_floor = ecpm_floor
  }

  preloadAd() {
    return new Promise((resolve, reject) => {
      // adsdk.log(`vivoAdRequest preloadAd ${this.adUnitId} ${Date.now()}`)
      ad.preloadAd({
        adUnitId: this.adUnitId,
        adCount: 1,
        ecpm: true,
        type: 'native',
        success: data => {
          this.adData = data.adList[0]
          if (this.adData.price > 0) {
            this.ecpm = this.adData.price
          } else {
            this.ecpm = this.ecpm_floor
          }
          // adsdk.log(`vivoAdRequest success ${this.adUnitId} ${Date.now()}`)
          resolve(this.adData)
        },
        fail: (data, code) => {
          if (code === 205) {
            this.adData = data.adList[0]
            if (this.adData.price > 0) {
              this.ecpm = this.adData.price
            } else {
              this.ecpm = this.ecpm_floor
            }
            // adsdk.log(`vivoAdRequest success ${this.adUnitId} ${Date.now()}`)
            resolve(this.adData)
          } else {
            adsdk.log(
              `data: ${JSON.stringify(data)} code: ${code} ${this.adUnitId}`
            )
            try {
              const failReportPercent =
                config.getAdConfig()['fail_report_percent']
              if (
                failReportPercent &&
                Math.random() * 100 < failReportPercent
              ) {
                trackEvent({
                  category: 'advertise',
                  action: 'fail',
                  opt_label: 'native_ad',
                  opt_extra: {
                    unit_id: this.adUnitId,
                    code: data.errCode,
                    err_msg: data.errMsg,
                  },
                })
              }
            } catch (e) {}
            resolve(null)
          }
        },
      })
    })
  }

  isDownloadAd() {
    return this.adData.interactionType === 2
  }

  sendWinNotification(lossEcpm) {
    let biddingParams = {
      adid: this.adData.adId,
      adType: 'native',
      price: parseInt(this.adData.price),
    }
    ad.sendWinNotification(biddingParams)
  }

  sendLossNotification(winEcpm) {
    let biddingParams = {
      adid: this.adData.adId, // 预加载返回的广告位id
      adType: 'native', // 原生信息流广告
      price: parseInt(winEcpm) || 0, // 竞胜方价格
      reason: 1, // 竞败原因
      isDelete: true, // 是否删除该条竞败广告资源
    }
    ad.sendLossNotification(biddingParams)
  }
}

export default VivoAd
