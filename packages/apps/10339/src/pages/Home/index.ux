<import name="draw-page" src="../../components/DrawPage/index.ux"></import>
<import
  name="billing-page"
  src="../../components/BillingPage/index.ux"
></import>
<import name="mine-page" src="../../components/MinePage.ux"></import>
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<import name="weather-page" src="@/weather/index.ux"></import>
<import name="clean-page" src="../../clean/index.ux"></import>

<template>
  <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
    <tab-content class="content">
      <div class="content-box" for="item in pageList" tid="$idx">
        <component
          id="{{item.pageComponent}}"
          is="{{item.pageComponent}}"
          index="{{$idx}}"
          current-index="{{currentIndex}}"
        ></component>
      </div>
    </tab-content>
    <tab-bar class="tab-bar">
      <div for="item in pageList" tid="$idx" class="tab-bar-item">
        <block if="{{item.pageComponent === 'book-keep-page'}}">
          <div class="center-tab">
            <image class="icon" src="/assets/images/ic_add.png"></image>
          </div>
          <text class="tab-text">{{ item.text }}</text>
        </block>
        <block else>
          <!-- <image
            if="{{currentIndex === $idx}}"
            class="tab-bar-icon-active"
            src="{{item.selectedIconPath}}"
          />
          <image else class="tab-bar-icon" src="{{item.iconPath}}" /> -->
          <text class="tab-text">{{ item.text }}</text>
        </block>
      </div>
    </tab-bar>
    <service></service>
  </tabs>
</template>

<script>
import device from '@quickapp/business/lib/device'
import { trackEvent } from '@quickapp/business'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'
import { storage } from '@quickapp/utils'
import config from '@quickapp/business/lib/config'

export default setPageMenuConfig({
  private: {
    pageList: [
      {
        pageComponent: 'billing-page',
        text: '记账',
      },
      {
        pageComponent: 'draw-page',
        text: '背单词',
      },
      {
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
    title: '',
    optionsList: [],
  },

  async onInit() {
    await config.updateConfig()
    let showClean = config.getAdConfig()['show_clean']
    console.log('show clean: ', showClean)
    if (showClean) {
      this.pageList = [
        {
          iconPath: '/clean/assets/ic_tab_clean.webp',
          selectedIconPath: '/clean/assets/ic_tab_clean_active.webp',
          pageComponent: 'clean-page',
          text: '清理',
        },
        ...this.pageList,
      ]
    }
    let showWeather = config.getAdConfig()['show_weather']
    if (showWeather) {
      this.pageList = [
        {
          iconPath: '/weather/assets/ic_tab_weather.webp',
          selectedIconPath: '/weather/assets/ic_tab_weather_active.webp',
          pageComponent: 'weather-page',
          text: '天气',
        },
        ...this.pageList,
      ]
      this.currentIndex = -1
      setTimeout(() => {
        this.currentIndex = 0
      })
    }
  },

  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })

    const Drawpage = this.$child('draw-page')
    if (Drawpage) {
      Drawpage.getInitData()
    }

    const Billingpage = this.$child('billing-page')
    if (Billingpage) {
      Billingpage.getInitData()
    }
  },

  onBackPress() {
    return false
  },
  handleAdClose() {},

  handleChange(evt) {
    this.currentIndex = evt.index
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';

.wrapper {
  width: 100%;
  height: 100%;
  /* background-color: #f8f8f8; */
  background-image: url('/assets/images/dt_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  background-color: #060039;
}

.center-tab {
  justify-content: center;
  align-items: center;
  background-color: #060039;
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;
  background-color: #060039;

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 60px;
    height: 60px;
  }

  .tab-text {
    text-align: center;
    color: #ffffff;
    line-height: 100px;
    font-size: 32px;
  }

  .tab-text:active {
    color: #e53855;
  }
}
</style>
