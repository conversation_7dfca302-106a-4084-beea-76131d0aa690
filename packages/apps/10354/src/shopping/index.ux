<template>
  <div class="wrapper">
    <list class="shopping-list">
      <list-item class="shopping-item-wrapper" for="item in items">
        <div class="shopping-item" @click="onItemClick(item)">
          <image src="{{item.image}}"></image>
          <div class="shopping-item-content">
            <text class="name">{{ item.name }}</text>
            <div class="price-container">
              <text class="price">{{ item.price }}</text>
              <text class="similar-button">领取优惠券</text>
            </div>
          </div>
        </div>
      </list-item>
    </list>
  </div>
</template>

<script>
import router from '@system.router'
import list from '@/shopping/list'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data: {
    items: list,
  },
  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  onReady() {},

  onDestroy() {},

  onItemClick(item) {
    router.push({
      uri: 'shopping/detail',
      params: item,
    })
  },

  handleStorageChange() {
    this.getInitData()
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },
  async getInitData() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fefefe;
}

.header {
  padding: 20px;
  height: 200px;
  justify-content: center;
  align-items: center;
}

.header-text {
  color: white;
  font-size: 64px;
  font-weight: bold;
}

.shopping-list {
  columns: 2;
  flex: 1;
  padding: 10px;
  margin-top: 80px;
}

.shopping-item-wrapper {
  padding: 10px;
  break-inside: avoid;
}

.shopping-item {
  flex-direction: column;
  overflow: hidden;

  image {
    width: 100%;
    height: 420px;
  }

  &-content {
    padding: 15px;
    flex-direction: column;

    .name {
      font-size: 28px;
      color: #333333;
      lines: 2;
      text-overflow: ellipsis;
    }

    .price-container {
      margin-top: 10px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .price {
        font-size: 36px;
        color: #333333;
        font-weight: bold;
      }

      .similar-button {
        padding: 5px 15px;
        color: #ff4d4f;
        font-size: 24px;
      }
    }
  }
}
</style>
