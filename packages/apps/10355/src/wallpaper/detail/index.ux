<template>
  <div class="page">
    <image src="{{image}}" class="bg-image"></image>
    <div class="content">
      <image src="{{image}}" class="preview-image"></image>
      <div class="button" onclick="downloadWallpaper">
        <text class="button-text">下载壁纸</text>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@system.request'
import { showToast } from '@quickapp/utils'
import media from '@system.media'

export default {
  data: {
    image: '',
    cacheUri: '',
  },
  onInit: function () {},
  downloadWallpaper: function () {
    this.download()
      .then(data => {
        this.cacheUri = data.uri
        return this.saveToPhotosAlbum(this.cacheUri)
      })
      .then(res => {
        showToast(res)
      })
      .catch(err => {
        showToast(err)
      })
  },

  download() {
    return new Promise((resolve, reject) => {
      request.download({
        url: this.image, // 只能下载http(s)的
        success: data => {
          request.onDownloadComplete({
            token: data.token,
            success: function (data) {
              resolve(data)
            },
            fail: function (data, code) {
              reject('下载失败，网络错误')
            },
          })
        },
        fail: (data, code) => {
          reject('下载失败，网络错误')
        },
      })
    })
  },

  saveToPhotosAlbum(uri) {
    return new Promise((resolve, reject) => {
      media.saveToPhotosAlbum({
        uri: uri,
        success: () => resolve('已保存在相册中'),
        fail: () => reject('保存失败,您可能未开权限'),
      })
    })
  },
}
</script>

<style>
.page {
  flex-direction: column;
  align-items: center;
  background-color: #000000;
}

.back-icon {
  position: absolute;
  top: 50px;
  left: 30px;
  width: 50px;
  height: 50px;
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(20px);
  opacity: 0.6;
}

.content {
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.preview-image {
  width: 80%;
  height: 70%;
  border-radius: 40px;
}

.button {
  margin-top: 60px;
  background-color: #2c2c2e;
  padding: 25px 80px;
  border-radius: 50px;
}

.button-text {
  color: #ffffff;
  font-size: 32px;
}
</style>
