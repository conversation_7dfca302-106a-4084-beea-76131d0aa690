<template>
  <div class="header">
    <div class="address" @click="toCityPage">
      <image class="ic-plus" src="/weather/assets/ic_plus.png"></image>
      <text class="tv-address">
        {{ locationInfo.district }} {{ locationInfo.street }}
      </text>
      <image class="ic-address" src="/weather/assets/ic_address.png"></image>
    </div>
    <!--不能0px;不能display:none-->
    <video
      style="width: 1px; height: 1px"
      id="video"
      @pause="handlePauseEvt"
      @start="handleStartEvt"
      @finish="handleFinisEvt"
      src="{{weatherNow.audioUrl}}"
    ></video>

    <div class="temperature">
      <div class="num-box">
        <weather-icon
          if="{{false}}"
          status="{{weatherNow.code}}"
          width="240px"
          height="240px"
        ></weather-icon>
        <div class="right">
          <div class="top">
            <text class="num">{{ weatherNow.temperature }}</text>
            <div class="unit"></div>
          </div>
          <div class="fotter">
            <text class="status">{{ weatherNow.text }}</text>
            <div class="ic-voice-box" if="{{false}}">
              <image
                class="ic-voice"
                @click="handleVoice"
                src="/weather/assets/ic_voice.png"
              />
              <div if="{{showDot}}" class="dot"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="warnUser">
        <div class="status-box">
          <image class="icon" src="/weather/assets/leaves.png"></image>
          <text class="status-txt">{{ airNow.quality }} {{ airNow.aqi }}</text>
        </div>
        <div class="status-box" if="{{false}}">
          <image class="icon" src="/weather/assets/taifeng.png"></image>
          <text class="status-txt">台风路径</text>
        </div>
        <div class="status-box warn-box" if="{{false}}">
          <image class="icon" src="/weather/assets/warn.png"></image>
          <text class="status-txt">天气预警</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import ad from '@service.ad'

export default {
  props: {
    locationInfo: {
      type: Object,
      default: () => ({
        province: '',
        city: '',
        district: '',
        street: '',
      }),
    },
    weatherNow: {
      type: Object,
      default: () => ({
        clouds: '0',
        code: '1',
        dew_point: '',
        feels_like: '27',
        humidity: '81',
        pressure: '1000',
        temperature: '24',
        text: '晴',
        time: null,
        visibility: '7.0',
        wind_direction: '西南',
        wind_direction_degree: '210',
        wind_scale: '1',
        wind_speed: '5.0',
        audioUrl: '',
      }),
    },
    airNow: {
      type: Object,
      default: () => ({
        aqi: '24',
        co: '0.542',
        last_update: '2022-07-20T22:00:00+08:00',
        no2: '19',
        o3: '42',
        pm10: '24',
        pm25: '12',
        primary_pollutant: '',
        quality: '优',
        so2: '6',
      }),
    },
  },

  data() {
    return {
      adData: null,
      isVideStart: false,
      showDot: true,
      isUnlock: false,
    }
  },

  onInit() {
    const provider = ad.getProvider()
    console.log('provider', provider)
    this.loadAd()
  },

  loadAd() {},

  toCityPage() {
    router.push({
      uri: 'weather/City',
    })
  },

  async handleVoice() {
    if (this.isUnlock) {
      this.handleVoiceInternal()
      return
    }
    if (this.adData != null) {
      const brand = ad.getProvider()
      if (brand.toLowerCase() === 'oppo') {
        showToast('即将进入激励视频')
        await this.delay(2000)
      }
      this.handleVoiceInternal()
      this.isUnlock = true
      this.loadAd()
    } else {
      this.handleVoiceInternal()
      this.loadAd()
    }
  },

  delay(t = 1000) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, t)
    })
  },

  handleVoiceInternal() {
    const video = this.$element('video')
    // const video = this.$child('video')
    this.showDot = false
    if (this.isVideStart) {
      video.pause()
    } else {
      video.start()
    }
  },

  handlePauseEvt() {
    this.isVideStart = false
  },

  handleStartEvt() {
    this.isVideStart = true
  },

  handleFinisEvt() {
    this.$element('video').setCurrentTime({ currenttime: 0 })
    this.isVideStart = false
  },
}
</script>

<style lang="less">
.header {
  flex-direction: column;
  width: 100%;
  height: 734px;
  padding-top: 104px;

  .address {
    padding-left: 32px;

    .ic-plus,
    .ic-address {
      width: 48px;
    }

    .tv-address {
      margin: 0 8px;
      font-size: 36px;
      font-weight: bolder;
      color: #111111;
      /* color: #000000; */
      line-height: 48px;
    }
  }

  .temperature {
    flex-direction: column;
    width: 496px;
    height: 728px;
    background-image: url('/weather/assets/header_bg.webp');
    background-size: cover;
    margin: 48px auto;
    .right {
      display: flex;
      flex-direction: row;
    }
    .num-box {
      flex-direction: row;
      margin: 110px auto 25px;
      .num {
        height: 160px;
        font-size: 160px;
        font-weight: 600;
        color: #ffffff;
        line-height: 160px;
      }

      .unit {
        width: 34px;
        height: 34px;
        border: 10px solid #ffffff;
        border-radius: 50%;
        margin-left: 18px;
      }

      .status {
        font-size: 56px;
        color: #ffffff;
        line-height: 56px;
        /* margin-left: -56px; */
        margin-right: 20px;
      }

      .ic-voice-box {
        /* visibility: hidden; */
        /* margin-left: auto; */
        /* align-self: flex-start; */
        /* margin-top: 20px; */
        .ic-voice {
          width: 64px;
          height: 64px;
        }

        .dot {
          width: 20px;
          height: 20px;
          background-color: red;
          border-radius: 50%;
          margin-left: -15px;
        }
      }
    }
    .warnUser {
      display: flex;
      margin: 0 auto;
      justify-content: space-between;
    }
    .status-box {
      justify-content: center;
      align-items: center;
      width: 188px;
      height: 64px;
      margin-top: 20px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 32px;
      .icon {
        width: 56px;
        height: 56px;
      }
      .status-txt {
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
      }
    }
    .warn-box {
      background-color: #f75540;
    }
  }
}
</style>
