<import name="my-header" src="./components/my-header.ux"></import>

<template>
  <div class="wrapper">
    <my-header
      location-info="{{locationInfo}}"
      weather-now="{{weatherNow}}"
      air-now="{{airNow}}"
    ></my-header>
    <div class="content">
      <div class="feature" for="{{features}}" @click="onFeatureClick($item)">
        <image src="{{$item.image}}" class="feature-image"></image>
        <text class="feature-name">{{ $item.name }}</text>
      </div>
    </div>
  </div>
</template>

<script>
import weather from '@/weather/api/weather'
import utils from '@/weather/helper/utils'
import router from '@system.router'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    lifeCycleShow: {
      type: Number,
    },
  },

  data: {
    locationInfo: {
      code: 'c1101',
      street: '北京市',
      district: '北京市',
      city: '北京市',
      province: '北京市',
      level: 2,
    },
    weatherNow: {
      clouds: '0',
      code: '1',
      dew_point: '',
      feels_like: '27',
      humidity: '81',
      pressure: '1000',
      temperature: '24',
      text: '晴',
      time: null,
      visibility: '7.0',
      wind_direction: '西南',
      wind_direction_degree: '210',
      wind_scale: '1',
      wind_speed: '5.0',
      audioUrl: '',
    },
    airNow: {
      aqi: '24',
      co: '0.542',
      last_update: '2022-07-20T22:00:00+08:00',
      no2: '19',
      o3: '42',
      pm10: '24',
      pm25: '12',
      primary_pollutant: '',
      quality: '优',
      so2: '6',
    },
    features: [
      {
        name: '天气',
        image: '/weather/assets/ic_feature_weather.webp',
        page: '/weather',
      },
      {
        name: '壁纸',
        image: '/weather/assets/ic_feature_wallpaper.webp',
        page: '/wallpaper',
      },
      {
        name: '决定大转盘',
        image: '/weather/assets/ic_feature_luck.webp',
        page: '/luck',
      },
      {
        name: '扫描',
        image: '/weather/assets/ic_feature_ocr.webp',
        page: '/ocr',
      },
      {
        name: '记账',
        image: '/weather/assets/ic_feature_billing.webp',
        page: '/billing',
      },
      {
        name: '尺子',
        image: '/weather/assets/ic_feature_ruler.webp',
        page: '/ruler',
      },
    ],
  },

  onInit() {
    // 执行逻辑
    // this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  onDestroy() {},

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getInitData()
    }
  },

  handleLifeCycleShow() {
    this.getInitData()
  },

  getInitData() {
    utils
      .getCityInfo()
      .then(cityInfo => {
        this.locationInfo = cityInfo

        const province = cityInfo.province
        const city = cityInfo.city
        const district = cityInfo.district
        const street = cityInfo.street
        const latitude = cityInfo.latitude
        const longitude = cityInfo.longitude

        const params = [province, city, city, city, latitude, longitude]

        weather.weatherViewNow(...params).then(res => {
          this.weatherNow = res.weather_now
          this.airNow = res.air_now
        })
      })
      .catch(err => {
        router.push({
          uri: 'weather/City',
        })
      })
  },
  onFeatureClick(item) {
    router.push({
      uri: item.page,
    })
  },
}
</script>

<style lang="less">
@import './assets/styles/style';

.wrapper {
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-image: url('./assets/bg.webp');
  background-size: cover;
}

.content {
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;

  .feature {
    background-image: url('./assets/bg_feature.webp');
    background-size: cover;
    width: 320px;
    height: 140px;
    flex-direction: row;
    align-items: center;
    margin: 15px;

    .feature-image {
      width: 68px;
      height: 68px;
      object-fit: contain;
      margin: 0 20px;
    }

    .feature-name {
      font-size: 32px;
      font-weight: bolder;
      color: #ffffff;
    }
  }
}
</style>
