<import name="billing-list" src="./components/billing-list.ux"></import>

<template>
  <div class="root">
    <div>
      <text class="title">记账</text>
    </div>
    <div class="total">
      <text>总收入:{{ formatNumber(totalIncome) }}</text>
      <text>总支出:{{ formatNumber(totalExpenses) }}</text>
    </div>
    <div class="record" @click="goAccount">
      <text>记一笔</text>
    </div>
    <div class="list" if="{{isBilling}}">
      <billing-list
        for="(index, item) in billingData"
        date="{{item.date}}"
        amount="{{item.amount}}"
        thing="{{item.thing}}"
        time="{{item.time}}"
        symbol="{{item.symbol}}"
        @edit="handleEditBilling"
      ></billing-list>
    </div>
  </div>
</template>

<script>
import { showToast, storage } from '@quickapp/utils'
import router from '@system.router'

export default {
  props: {
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },
  data: {
    billingData: [],
    isBilling: true,
  },

  onInit() {
    this.getInitData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      this.getInitData()
    }
  },

  computed: {
    totalExpenses() {
      if (!this.billingData) return 0
      const total = this.billingData.reduce((sum, item) => {
        return item.symbol === '-' ? sum + parseFloat(item.amount || 0) : sum
      }, 0)

      return parseFloat(total.toFixed(2))
    },

    totalIncome() {
      if (!this.billingData) return 0

      const total = this.billingData.reduce((sum, item) => {
        return item.symbol !== '-' ? sum + parseFloat(item.amount || 0) : sum
      }, 0)

      return parseFloat(total.toFixed(2))
    },
  },

  formatNumber(value) {
    const num =
      typeof value === 'string' ? parseFloat(value.replace(/,/g, '')) : value

    if (typeof num !== 'number' || isNaN(num)) {
      return value
    }

    if (num > 10000) {
      const formattedValue = (num / 10000).toFixed(2).replace(/\.?0+$/, '')
      return `${formattedValue}w`
    }

    return value
  },
  async getInitData() {
    this.isBilling = false
    await storage.get('billingData').then(res => {
      this.billingData = res
      console.log('billing_res', res)
    })

    this.isBilling = true
  },

  goAccount() {
    router.push({
      uri: 'pages/Account',
    })
  },

  handleEditBilling(items) {
    router.push({
      uri: 'pages/Account',
      params: items,
    })
  },
}
</script>

<style lang="less">
.root {
  flex-direction: column;
  padding: 120px 20px 0;
  width: 100%;
  /* background-color: #f4f4f4; */
  .title {
    font-size: 78px;
    color: #000;
  }
  .total {
    background: linear-gradient(90deg, #d83e94 0%, #facec0 100%);
    padding: 40px 20px;
    border-radius: 15px;
    display: flex;
    justify-content: space-around;
    text {
      color: #ffffff;
      font-size: 40px;
    }
  }
  .record {
    width: 100%;
    border: 1px solid #cecece;
    border-radius: 12px;
    background-color: #fff;
    justify-content: center;
    padding: 10px;
    margin-top: 20px;
    text {
      color: #000;
      font-size: 36px;
    }
  }
}
.list {
  width: 100%;
  flex-direction: column;
  margin-top: 15px;
}
</style>
