<template>
  <div class="wrapper">
    <div class="header">
      <div class="item">
        <image class="icon" src="/weather/assets/ic_sunrise.png"></image>
        <text class="desc">日出</text>
        <text class="time">{{ info.sunrise }}</text>
      </div>
      <div class="item">
        <image class="icon" src="/weather/assets/ic_sundown.png"></image>
        <text class="desc">日落</text>
        <text class="time">{{ info.sunset }}</text>
      </div>
    </div>
    <div class="content">
      <div class="item">
        <image class="icon" src="/weather/assets/ic_wind.png"></image>
        <div class="info">
          <text class="level">{{ info.level }}级</text>
          <text class="wind">{{ info.wind }}风</text>
        </div>
      </div>
      <div class="item">
        <image class="icon" src="/weather/assets/ic_feels_like.png"></image>
        <div class="info">
          <text class="level">{{ info.feelsLike }}°</text>
          <text class="wind">体感</text>
        </div>
      </div>
      <div class="item">
        <image class="icon" src="/weather/assets/ic_water.png"></image>
        <div class="info">
          <text class="level">{{ info.water }}%</text>
          <text class="wind">湿度</text>
        </div>
      </div>
      <div class="item">
        <image class="icon" src="/weather/assets/ic_seeing.png"></image>
        <div class="info">
          <text class="level">{{ info.seeing }}km</text>
          <text class="wind">能见度</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data: {
    title: 'Hello World. Quickapp Component.',
  },

  props: {
    info: {
      type: Object,
      default: () => ({
        sunrise: '05:51', // 日出
        sunset: '19:11', // 日落
        feelsLike: '', // 体感
        wind: '', // 风向
        water: '', // 湿度
        seeing: '', // 能见度
        level: '', // 等级
      }),
    },
  },

  onInit() {},
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  padding: 0 48px;
  background-color: #00bfff;
  border-radius: 24px;
  /* border: 1px solid rgba(192, 202, 216, 0.2); */
}
.header {
  height: 108px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaedfe;

  .icon {
    width: 48px;
    height: 48px;
  }

  .desc,
  .time {
    font-size: 32px;
  }

  .desc {
    color: #ffffff;
    margin-left: 32px;
  }

  .time {
    color: #ffffff;
    margin-left: 10px;
  }
}

.content {
  flex-wrap: wrap;

  .item {
    width: 50%;
    padding: 44px 0;

    .icon {
      width: 68px;
      height: 68px;
    }

    .info {
      flex-direction: column;
      margin-left: 24px;

      .level {
        font-size: 32px;
        font-weight: 500;
        color: #ffffff;
        line-height: 32px;
      }

      .wind {
        font-size: 28px;
        font-weight: 400;
        color: #ffffff;
        line-height: 28px;
        margin-top: 8px;
      }
    }
  }
}
</style>
