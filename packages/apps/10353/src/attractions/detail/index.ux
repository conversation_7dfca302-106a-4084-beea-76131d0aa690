<template>
  <div class="container">
    <text class="title">{{ name }}</text>
    <image src="{{image}}" class="attraction-image"></image>
    <text class="desc">{{ desc2 }}</text>
  </div>
</template>

<script>
export default {
  data: {
    id: '',
    name: '',
    image: '',
    desc: '',
    desc2: '',
  },
}
</script>

<style>
.container {
  flex-direction: column;
  padding: 20px;
}

.title {
  font-size: 50px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
}

.attraction-image {
  width: 100%;
  height: 500px;
  margin-bottom: 20px;
}

.desc {
  font-size: 32px;
  line-height: 48px;
}
</style>
