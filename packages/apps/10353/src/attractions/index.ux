<template>
  <div class="container">
    <image src="./assets/title.webp" class="title"></image>
    <list class="list" onscrollbottom="loadMoreData">
      <list-item for="{{attractionList}}" class="list-item">
        <div class="card" @click="goToDetail($item)">
          <image src="{{$item.image}}" class="attraction-image"></image>
          <div class="info">
            <text class="title">{{ $item.name }}</text>
            <div class="text">
              <div class="rating">
                <text class="star">★</text>
                <text class="star">5.0</text>
              </div>
              <text class="desc">{{ $item.desc }}</text>
            </div>
          </div>
        </div>
      </list-item>
    </list>
  </div>
</template>

<script>
import router from '@system.router'
import list from './list.js'

export default {
  data: {
    attractionList: [],
  },
  onInit() {
    this.attractionList = list
  },
  goToDetail(item) {
    router.push({
      uri: 'attractions/detail',
      params: {
        ...item,
      },
    })
  },
}
</script>

<style lang="less">
.container {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: white;
}

.title {
  width: 750px;
  height: 144px;
  margin-top: 40px;
}

.list {
  width: 100%;
  height: 100%;
}

.list-item {
  width: 100%;
  padding: 15px 30px;
  margin-bottom: 10px;
}

.card {
  width: 630px;
  margin: 0 auto;
  background-color: #ffffff;
  border: 2px solid #e4e4e4;
  flex-direction: column;
  border-radius: 48px;
}

.attraction-image {
  width: 100%;
  height: 378px;
}

.info {
  padding: 20px;
  flex-direction: column;
}

.title {
  height: 80px;
  font-size: 40px;
  font-weight: bold;
}

.rating {
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: #fef6e5;
  width: 128px;
  height: 64px;
  border-radius: 32px;
  margin-bottom: 10px;
}

.star {
  margin: 0 5px;
  color: #ffbb24;
  font-size: 30px;
}

.text {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.desc {
  font-size: 28px;
  color: #ffbb24;
}
</style>
