<template>
  <div class="wrapper">
    <div class="recommend">
      <swiper
        class="swiper"
        autoplay="true"
        index="{{swiperOpt.index}}"
        interval="{{swiperOpt.interval}}"
        indicator="{{swiperOpt.indicator}}"
      >
        <div
          class="swiper-item"
          for="value in hotList"
          tid="$idx"
          @click="toDesc(value)"
        >
          <image class="swiper-img" src="{{value.cover}}"></image>
          <div class="info">
            <text class="title">{{ value.name }}</text>
            <text class="desc">{{ value.introduction }}</text>
          </div>
        </div>
      </swiper>
    </div>

    <div class="type">
      <!--<div class="type__header">-->
      <!--  <image-->
      <!--    class="type__header__icon"-->
      <!--    src="/assets/images/ic_hot.webp"-->
      <!--  ></image>-->
      <!--  <text class="type__header__title">热门分类</text>-->
      <!--</div>-->
      <div class="type__list">
        <div
          class="type__list__item"
          for="item in categoryList"
          @click="toListPage(item)"
        >
          <image
            class="type__list__item__cover"
            src="{{pyMap && pyMap[item.category] && pyMap[item.category].img}}"
          ></image>
          <text class="type__list__item__label">{{ item.category }}</text>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBookstoreData } from '@/api/bookstore'
import router from '@system.router'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data: {
    swiperOpt: {
      index: 0,
      interval: 5000,
      indicator: false,
    },
    hotList: [
      {
        id: 2,
        cover: '/assets/images/logo.png',
        name: '开局地摊卖大力',
        introduction: '地复苏时代',
      },
      {
        id: 2,
        cover: '/assets/images/logo.png',
        name: '开局地摊卖大力',
        introduction: '地复苏时代',
      },
    ],
    categoryList: [],
    pyMap: {
      同人: {
        py: 'KUAICHUANTONGREN',
        img: '/assets/images/category/bg_kctr.png',
        bgImg: 'bg_kctr.png',
      },
      历史: {
        py: 'JIAKONGLISHI',
        img: '/assets/images/category/bg_jkls.png',
        bgImg: 'bg_jkls.png',
      },
      动漫: {
        py: 'YOUXIDONGMAN',
        img: '/assets/images/category/bg_yxdm.png',
        bgImg: 'bg_yxdm.png',
      },
      都市: {
        py: 'DUSHIYANQING',
        img: '/assets/images/category/bg_dsyq.png',
        bgImg: 'bg_dsyq.png',
      },
      玄幻: {
        py: 'XUANHUANXIUZHEN',
        img: '/assets/images/category/bg_xhxz.png',
        bgImg: 'bg_xhxz.png',
      },
      穿越: {
        py: 'GUJINCHUANYUE',
        img: '/assets/images/category/bg_gjcy.png',
        bgImg: 'bg_gjcy.png',
      },
    },
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
    isCurrentPage() {
      return this.index === this.currentIndex
    },
  },

  onInit() {
    this.getData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },
  getData() {
    getBookstoreData().then(res => {
      if (res) {
        if (res.hotRecommend) {
          this.hotList = res.hotRecommend
        }
        if (res.categoryList) {
          this.categoryList = res.categoryList
          console.log('this.categoryList', this.categoryList)
        }
      }
    })
  },
  toListPage(item) {
    router.push({
      uri: 'pages/BookList',
      params: {
        title: item.category,
        id: item.id,
      },
    })
  },
  toDesc(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';

.wrapper {
  width: 100%;
  flex-direction: column;
}

.recommend {
  flex-direction: column;
  align-items: center;
  margin-top: 136px;

  &__header {
    width: 750px;
    height: 500px;
    background: linear-gradient(180deg, #e0e5fd 0%, #ffffff 100%);
  }

  .swiper {
    width: 670px;
    height: 520px;
    border-radius: 20px;
    indicator-bottom: 20px;
    indicator-size: 12px;
    indicator-selected-color: #ed5b3b;
    indicator-color: #d8d8d8;
  }

  .swiper-item {
    flex-direction: column;
    align-items: center;
    height: 100%;
    border-radius: 8px;

    .swiper-img {
      width: 100%;
      height: 100%;
      border-radius: 8px;
    }

    .info {
      width: 100%;
      height: 122px;
      flex-direction: column;
      justify-content: center;
      margin-top: -100px;
      padding: 0 32px;
      box-sizing: border-box;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.5) 100%
      );
      border-bottom-left-radius: 32px;
      border-bottom-right-radius: 32px;

      .title {
        font-size: 32px;
        font-weight: bolder;
        lines: 1;
        text-overflow: ellipsis;
        color: #ffffff;
      }

      .desc {
        font-size: 24px;
        line-height: 36px;
        lines: 1;
        text-overflow: ellipsis;
        margin-top: 8px;
        color: white;
      }
    }
  }
}

.type {
  flex-direction: column;
  margin-top: 64px;

  &__header {
    align-items: center;
    padding: 0 32px;

    &__icon {
      width: 48px;
      height: 48px;
    }

    &__title {
      font-size: 36px;
      font-weight: bold;
      margin-left: 16px;
    }
  }

  &__list {
    flex-wrap: wrap;
    padding: 0 40px;
    justify-content: space-between;
    margin-top: 42px;

    &__item {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-bottom: 18px;
      background-color: #fff;
      width: 324px;
      height: 160px;
      border-radius: 32px;
      border: 1px solid #eee;
      //background-image: url("/assets/images/category/bg_kctr.png");
      background-size: 100%;

      &__cover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
      }

      &__label {
        font-size: 44px;
        font-weight: bold;
        color: #ffffff;
      }

      &__py {
        font-size: 20px;
        color: #999999;
        margin-top: 8px;
      }
    }
  }
}
</style>
