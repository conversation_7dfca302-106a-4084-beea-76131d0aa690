<import
  name="table-content"
  src="../../../components/table-content.ux"
></import>
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>

<template>
  <div id="drawer" class="page">
    <list class="wrapper">
      <list-item type="container" class="container">
        <div class="container-inner-box">
          <div class="info">
            <text class="title">{{ info.name }}</text>
            <text class="author">{{ info.author }}</text>
          </div>
          <div class="introduction">
            <text class="title">简介</text>
            <text class="content">
              {{ info.introduction }}
            </text>
          </div>
          <div class="ad-box">
            <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
          </div>
          <div class="table-contents" @click="openDrawer">
            <text class="title">目录</text>
            <div class="table-contents__chapter">
              <text class="content">已完结 共{{ info.chapterCount }}章</text>
              <image
                class="icon"
                src="/assets/images/ic_arrow_right.webp"
              ></image>
            </div>
          </div>
          <div class="btn-box">
            <div class="read-btn" @click="toDetail">
              <image src="/assets/images/ic_listen.webp"></image>
              <text>开始听书</text>
            </div>
          </div>
        </div>
        <image class="cover" src="{{info.cover}}"></image>
      </list-item>
    </list>
    <apex-popup id="popup" position="left">
      <table-content
        id="table-content"
        @click-item="handleClickItem"
      ></table-content>
    </apex-popup>
  </div>
</template>

<script>
import { getBookInfoData } from '@/api/bookInfo'
import router from '@system.router'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
    },
    showDraw: false,
  },

  protected: {
    id: 2, // 小说id
  },

  onInit() {},

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
      }
    })
  },

  openDrawer() {
    this.$child('popup').show()
    this.$child('table-content').initData(this.id)
    this.showDraw = true
  },

  closeDrawer() {
    this.$child('popup').hide()
    this.showDraw = false
  },

  handleClickItem({ detail }) {
    this.closeDrawer()

    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.data.bookId,
        chapterId: detail.data.id,
        name: this.info.name,
      },
    })
  },

  toDetail() {
    const toPage = params => {
      router.push({
        uri: 'pages/Content/Detail',
        params,
      })
    }

    const params = {
      bookId: this.info.id,
      chapterId: this.info.chapterId || this.info.firstChapterId,
      name: this.info.name,
    }

    toPage(params)
  },
}

// const m_global = global.__proto__ || global
//
// if (m_global.is_from_playable) {
//   pageConfig.onMenuPress = function () {}
// }

export default setPageMenuConfig(pageConfig)
</script>

<style lang="less">
@import '../../../assets/styles/style';
.page {
  flex-direction: column;
  justify-content: center;
}

.wrapper {
  flex-direction: column;
  height: 100%;
}

.header {
  width: 100%;
  justify-content: center;
}

.cover {
  position: absolute;
  width: 346px;
  height: 460px;
  align-self: center;
}

.container {
  flex-direction: column;
  background-color: rgba(66, 71, 192, 1);
}
.container-inner-box {
  padding-top: 358px;
  flex-direction: column;
  margin-top: 100px;
  background-color: white;
  border-top-left-radius: 64px;
  border-top-right-radius: 64px;
}

.info {
  flex-direction: column;
  align-items: center;
  padding-bottom: 40px;
  margin: 40px 40px 0;

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #333333;
    line-height: 56px;
  }

  .author {
    font-size: 28px;
    color: #999999;
    line-height: 40px;
    margin-top: 16px;
  }
}

.introduction {
  flex-direction: column;
  margin: 0 40px;

  .title {
    font-size: 32px;
    font-weight: bold;
  }

  .content {
    text-align: justify;
    margin-top: 20px;
    font-size: 28px;
    color: #999;
    line-height: 48px;
  }
}

.table-contents {
  margin: 98px 40px 20px;
  justify-content: space-between;
  align-items: center;

  .title {
    font-size: 32px;
    font-weight: bold;
    color: #333333;
  }

  &__chapter {
    .content {
      height: 28px;
      color: #999;
    }
    .icon {
      width: 32px;
    }
  }
}

.ad-box {
  align-items: center;
  justify-content: center;
  width: 100%;
  border-radius: 20px;
  //margin: 40px auto 0;
}

.btn-box {
  justify-content: center;
  padding-bottom: 80px;
  margin-top: 40px;

  .read-btn {
    align-items: center;
    justify-content: center;
    width: 670px;
    height: 108px;
    background: linear-gradient(145deg, #7a90a8 0%, #a5b9d3 100%);
    border-radius: 16px;

    text {
      color: white;
      margin-left: 18px;
    }

    image {
      width: 44px;
    }
  }
}
</style>
