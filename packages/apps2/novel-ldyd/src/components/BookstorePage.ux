<template>
  <div class="wrapper">
    <div class="recommend">
      <swiper
        class="swiper"
        autoplay="true"
        index="{{swiperOpt.index}}"
        interval="{{swiperOpt.interval}}"
        indicator="{{swiperOpt.indicator}}"
      >
        <div
          class="swiper-item"
          for="value in hotList"
          tid="$idx"
          @click="toDesc(value)"
        >
          <image class="swiper-img" src="{{value.cover}}"></image>
          <div class="info">
            <text class="title">{{ value.name }}</text>
            <text class="desc">{{ value.category }}</text>
          </div>
        </div>
      </swiper>
    </div>

    <div class="type">
      <!--<div class="type__header">-->
      <!--  <image-->
      <!--    class="type__header__icon"-->
      <!--    src="/assets/images/ic_hot.webp"-->
      <!--  ></image>-->
      <!--  <text class="type__header__title">热门分类</text>-->
      <!--</div>-->
      <div class="type__list">
        <div
          class="type__list__item"
          for="item in categoryList"
          @click="toListPage(item)"
        >
          <text class="type__list__item__label">{{ item.category }}</text>
          <image
            class="type__list__item__icon"
            src="{{categoryMap[item.category]}}"
          ></image>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBookstoreData } from '@/api/bookstore'
import router from '@system.router'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data: {
    swiperOpt: {
      index: 0,
      interval: 5000,
      indicator: false,
    },
    hotList: [
      {
        id: 2,
        cover: '/assets/images/logo.png',
        name: '开局地摊卖大力',
        introduction: '地复苏时代',
      },
      {
        id: 2,
        cover: '/assets/images/logo.png',
        name: '开局地摊卖大力',
        introduction: '地复苏时代',
      },
    ],
    categoryList: [],
    pyMap: {
      同人: 'KUAICHUAN',
      历史: 'JIAKONG',
      动漫: 'YOUXI',
      都市: 'DUSHI',
      玄幻: 'XUANHUAN',
      穿越: 'CHUANYUE',
    },
    categoryMap: {
      同人: '/assets/images/category/kctr.webp',
      历史: '/assets/images/category/jkls.webp',
      动漫: '/assets/images/category/yxdm.webp',
      都市: '/assets/images/category/dsyq.webp',
      玄幻: '/assets/images/category/xhxz.webp',
      穿越: '/assets/images/category/gjcy.webp',
    },
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
    isCurrentPage() {
      return this.index === this.currentIndex
    },
  },

  onInit() {
    this.getData()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },
  getData() {
    getBookstoreData().then(res => {
      if (res) {
        if (res.hotRecommend) {
          this.hotList = res.hotRecommend
          console.log('this.hotList')
          console.log(JSON.stringify(this.hotList))
        }
        if (res.categoryList) {
          this.categoryList = res.categoryList
          console.log('this.categoryList', this.categoryList)
        }
      }
    })
  },
  toListPage(item) {
    router.push({
      uri: 'pages/BookList',
      params: {
        title: item.category,
        id: item.id,
      },
    })
  },
  toDesc(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
@import '../assets/styles/style';

.wrapper {
  width: 100%;
  flex-direction: column;
  background-color: #f9f7f5;
}

.recommend {
  flex-direction: column;
  align-items: center;

  &__header {
    width: 750px;
    height: 500px;
    background: linear-gradient(180deg, #e0e5fd 0%, #ffffff 100%);
  }

  .swiper {
    height: 620px;
    indicator-bottom: 20px;
    indicator-size: 12px;
    indicator-selected-color: #ed5b3b;
    indicator-color: #d8d8d8;
  }

  .swiper-item {
    flex-direction: column;
    align-items: center;
    height: 100%;

    .swiper-img {
      width: 100%;
      height: 504px;
    }

    .info {
      width: 100%;
      height: 108px;
      align-items: center;
      justify-content: space-between;
      padding: 0 32px;
      background-color: #fff;
      box-sizing: border-box;
      border-bottom: 1px solid #f1f1f1;

      .title {
        font-size: 32px;
        font-weight: bolder;
        lines: 1;
        color: #333333;
        text-overflow: ellipsis;
      }

      .desc {
        font-size: 24px;
        line-height: 36px;
        color: #000;
        lines: 1;
        text-overflow: ellipsis;
        margin-top: 16px;
      }
    }
  }
}

.type {
  flex-direction: column;
  margin-top: 64px;

  &__header {
    align-items: center;
    padding: 0 32px;

    &__icon {
      width: 48px;
      height: 48px;
    }

    &__title {
      font-size: 36px;
      font-weight: bold;
      margin-left: 16px;
    }
  }

  &__list {
    flex-wrap: wrap;
    padding: 0 32px;
    justify-content: space-between;

    &__item {
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      background-color: #ffffff;
      padding: 0 32px;
      width: 332px;
      height: 160px;
      border-radius: 8px;

      &__cover {
        width: 110px;
        height: 136px;
        border-radius: 20px;
      }

      &__label {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        line-height: 44px;
      }

      &__icon {
        width: 90px;
      }

      &__py {
        font-size: 20px;
        color: #999999;
        line-height: 28px;
        margin-top: 8px;
      }

      &__modification {
        position: absolute;
        right: -106px;
        top: -106px;
        width: 212px;
        height: 212px;
        background-color: #ffd100;
        border-radius: 212px;
      }
    }
  }
}
</style>
