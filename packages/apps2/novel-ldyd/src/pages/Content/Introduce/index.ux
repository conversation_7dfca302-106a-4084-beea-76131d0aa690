<import
  name="table-content"
  src="../../../components/table-content.ux"
></import>
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>

<template>
  <div id="drawer" class="page">
    <list class="wrapper">
      <list-item type="list-header" class="header">
        <image class="cover" src="{{info.cover}}"></image>
        <text class="title">{{ info.name }}</text>
        <text class="author">{{ info.author }}</text>
      </list-item>

      <!--<list-item type="info" class="info">-->
      <!--  <text class="title">{{ info.name }}</text>-->
      <!--  <text class="author">{{ info.author }}</text>-->
      <!--</list-item>-->

      <list-item type="list-introduction" class="introduction">
        <text class="title">简介</text>
        <text class="content">
          {{ info.introduction }}
        </text>
      </list-item>

      <list-item type="list-ad" class="ad-box">
        <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
      </list-item>

      <list-item
        type="list-contents"
        class="table-contents"
        @click="openDrawer"
      >
        <div class="table-contents__chapter">
          <text class="title">目录</text>
          <text class="content">已完结 共{{ info.chapterCount }}章</text>
          <text class="icon">></text>
        </div>
      </list-item>

      <list-item class="btn-box" type="list-item">
        <div class="read-btn" @click="toDetail">
          <!--<image src="/assets/images/ic_listen.webp"></image>-->
          <text>开始听书</text>
        </div>
      </list-item>
    </list>
    <apex-popup id="popup" position="left">
      <table-content
        id="table-content"
        @click-item="handleClickItem"
      ></table-content>
    </apex-popup>
  </div>
</template>

<script>
import { getBookInfoData } from '@/api/bookInfo'
import router from '@system.router'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
    },
    showDraw: false,
  },

  protected: {
    id: 2, // 小说id
  },

  onInit() {},

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
      }
    })
  },

  openDrawer() {
    this.$child('popup').show()
    this.$child('table-content').initData(this.id)
    this.showDraw = true
  },

  closeDrawer() {
    this.$child('popup').hide()
    this.showDraw = false
  },

  handleClickItem({ detail }) {
    this.closeDrawer()

    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.data.bookId,
        chapterId: detail.data.id,
        name: this.info.name,
      },
    })
  },

  toDetail() {
    const toPage = params => {
      router.push({
        uri: 'pages/Content/Detail',
        params,
      })
    }

    const params = {
      bookId: this.info.id,
      chapterId: this.info.chapterId || this.info.firstChapterId,
      name: this.info.name,
    }

    toPage(params)
  },
}

// const m_global = global.__proto__ || global
//
// if (m_global.is_from_playable) {
//   pageConfig.onMenuPress = function () {}
// }

export default setPageMenuConfig(pageConfig)
</script>

<style lang="less">
@import '../../../assets/styles/style';
.page {
  flex-direction: column;
  justify-content: center;
  background-color: @mainBgColor;
}

.wrapper {
  flex-direction: column;
  height: 100%;
}

.header {
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;

  .cover {
    width: 346px;
    margin-bottom: 50px;
    border-radius: 8px;
  }

  .title {
    font-size: 40px;
    font-weight: bolder;
    line-height: 56px;
    margin-bottom: 16px;
  }
}

.info {
  flex-direction: column;
  padding-bottom: 40px;
  margin: 64px 40px 0;
  border-bottom: 2px solid #eeeeee;

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #333333;
    line-height: 56px;
  }

  .author {
    font-size: 28px;
    color: #999999;
    line-height: 40px;
    margin-top: 16px;
  }
}

.introduction {
  flex-direction: column;
  margin: 40px;
  padding-bottom: 40px;
  border-bottom: 2px solid #f1f1f1;

  .title {
    font-size: 36px;
    font-weight: bold;
    color: #000;
  }

  .content {
    text-align: justify;
    margin-top: 20px;
    font-size: 28px;
    color: #999;
    line-height: 48px;
  }
}

.table-contents {
  flex-direction: column;
  margin: 0 40px;
  border-bottom: 2px solid #eeeeee;
  padding-bottom: 40px;

  .title {
    font-size: 36px;
    font-weight: bold;
    color: #000;
    line-height: 44px;
  }

  &__chapter {
    //justify-content: space-between;
    align-items: center;

    .content {
      width: 228px;
      height: 28px;
      font-size: 28px;
      color: rgba(0, 0, 0, 0.7);
      margin-left: auto;
    }
    .icon {
      font-size: 28px;
      color: rgba(0, 0, 0, 0.7);
    }
  }
}

.ad-box {
  align-items: center;
  justify-content: center;
  width: 100%;
  border-radius: 20px;
  //margin: 40px auto 0;
}

.btn-box {
  justify-content: center;
  padding-bottom: 80px;
  margin-top: 40px;

  .read-btn {
    align-items: center;
    justify-content: center;
    border: 2px solid #333333;
    width: 670px;
    height: 108px;
    background-color: #30323e;
    border-radius: 8px;

    text {
      color: white;
      margin-left: 18px;
    }

    image {
      width: 44px;
    }
  }
}
</style>
