<import name="bookstore-page" src="../../components/BookstorePage.ux"></import>
<import name="bookshelf-page" src="../../components/BookshelfPage.ux"></import>
<import name="mine-page" src="../../components/MinePage.ux"></import>
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import> -->

<template>
  <div>
    <!-- <intad
      id="intad"
      event-name="ad_home_insert"
      @close="handleAdClose"
    ></intad> -->
    <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
      <tab-content class="content">
        <div class="content-box" for="item in pageList" tid="$idx">
          <component
            is="{{item.pageComponent}}"
            index="{{$idx}}"
            current-index="{{currentIndex}}"
          ></component>
        </div>
      </tab-content>
      <tab-bar class="tab-bar">
        <div for="item in pageList" tid="$idx" class="tab-bar-item">
          <block if="{{item.pageComponent === 'book-keep-page'}}">
            <div class="center-tab">
              <image class="icon" src="/assets/images/ic_add.png"></image>
            </div>
            <text class="tab-text">{{ item.text }}</text>
          </block>
          <block else>
            <image
              if="{{currentIndex === $idx}}"
              class="tab-bar-icon-active"
              src="{{item.selectedIconPath}}"
            />
            <image else class="tab-bar-icon" src="{{item.iconPath}}" />
            <text class="tab-text">{{ item.text }}</text>
          </block>
        </div>
      </tab-bar>
      <service bottom="350"></service>
    </tabs>
  </div>
</template>

<script>
import { trackEvent } from '@quickapp/business'
import device from '@quickapp/business/lib/device'
import adsdk from '@quickapp/business/lib/adsdk'
import { initHandle, protectedObj } from '@quickapp/utils/lib/toReaderPage'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {
  private: {
    pageList: [
      {
        iconPath: '/assets/images/ic_bookshelf.webp',
        selectedIconPath: '/assets/images/ic_bookshelf_active.webp',
        pageComponent: 'bookshelf-page',
        text: '书架',
      },
      {
        iconPath: '/assets/images/ic_bookstore.webp',
        selectedIconPath: '/assets/images/ic_bookstore_active.webp',
        pageComponent: 'bookstore-page',
        text: '书城',
      },
      {
        iconPath: '/assets/images/ic_mine.webp',
        selectedIconPath: '/assets/images/ic_mine_active.webp',
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
    rewardCount: 0,
  },

  protected: {
    ...protectedObj,
  },

  computed: {
    title() {
      return this.pageList[this.currentIndex].text
    },
  },

  onInit() {
    initHandle.bind(this)()
  },

  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })
    // this.$child('intad').handleShow()
  },
  onBackPress() {
    return false
  },

  handleAdClose() {
    let intad = this.$child('intad')
    if (intad && intad.showBackAd) {
      intad.showBackAd()
    }
  },

  handleChange(evt) {
    this.currentIndex = evt.index
  },
}

// const m_global = global.__proto__ || global
//
// if (m_global.is_from_playable) {
//   pageConfig.onMenuPress = function () {}
// }

export default setPageMenuConfig(pageConfig)
</script>

<style lang="less">
@import '../../assets/styles/style.less';

.wrapper {
  width: 100%;
  height: 100%;
  background-color: @mainBgColor;
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  background-color: white;
}

.center-tab {
  justify-content: center;
  align-items: center;
  background-color: #fbdf4c;
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 52px;
    height: 52px;
  }

  .tab-text {
    text-align: center;
    color: #333333;
  }

  .tab-text:active {
    //color: @mainLightColor;
  }
}
</style>
