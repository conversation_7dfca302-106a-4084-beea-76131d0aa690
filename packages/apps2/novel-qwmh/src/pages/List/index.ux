<import name="my-spin" src="@quickapp/apex-ui/components/spin/index"></import>
<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div class="wrapper">
    <div class="header">
      <image
        @click="back"
        class="header__back"
        src="/assets/images/ic_back2.webp"
      ></image>
      <text class="header__title">{{ title }}</text>
    </div>
    <refresh
      class="refresh"
      offset="132px"
      refreshing="{{pulldownrefreshing}}"
      @refresh="handlePulldownrefresh"
    >
      <my-spin tip="加载中..." nested="{{true}}" loading="{{firstLoading}}">
        <list slot="nested" class="list" @scrollbottom="handlePulluprefresh">
          <block if="{{dataList && dataList.length}}">
            <list-item
              class="item"
              type="listItem"
              for="{{dataList}}"
              tid="$idx"
              @click="toRead($item)"
            >
              <image class="img" src="{{$item.cover}}"></image>
              <div class="info">
                <text class="title">
                  {{ $item.name }}
                </text>
                <text class="desc">{{ $item.introduction }}</text>
                <div style="margin-top: auto">
                  <text class="type">{{ title }}</text>
                  <text class="chapter">共{{ $item.chapterCount }}章</text>
                </div>
              </div>
            </list-item>
            <list-item class="loading-box" type="loading">
              <text if="{{pulluprefreshing}}">正在加载...</text>
              <text elif="{{!loadMoreEnabled}}">
                ------ 我是有底线的 ------
              </text>
            </list-item>
          </block>
          <list-item else type="no-data" class="no-data">
            <image src="/assets/images/no_data.png"></image>
            <text>暂无数据</text>
          </list-item>
          <!-- 占位符 -->
          <list-item
            if="{{showShortcutBtn}}"
            type="footer"
            style="height: 156px"
          ></list-item>
        </list>
      </my-spin>
    </refresh>
    <div class="add-desk">
      <!--<short-cut></short-cut>-->
    </div>
  </div>
</template>

<script>
import shortcut from '@system.shortcut'
import router from '@system.router'
import { getBookStore } from '@/api/comics'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    pulluprefreshing: false,
    pulluprefresh: false,
    pulldownrefreshing: false,
    dataList: new Array(10).fill({
      name: '标题',
      introduction:
        '介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍介绍',
      cover: '/assets/images/2.webp',
      type: '玄幻修仙',
      chapterCount: '1000章',
    }),
    // 总页数
    currentTotalPages: Number.MAX_SAFE_INTEGER,
    // 当前页
    currentPage: 1,
    pageSize: 10,
    showShortcutBtn: true,
    firstLoading: false,
    eventbutton: 'eventbutton',
    eventtype: 'shortcut',
    isOPPO: true,
  },

  protected: {
    title: '热门漫画',
    id: 2,
  },

  onShow() {
    shortcut.hasInstalled({
      success: result => {
        this.showShortcutBtn = !result
      },
    })
  },

  computed: {
    loadMoreEnabled: {
      get() {
        return this.currentPage < this.currentTotalPages
      },
    },
  },

  onInit() {
    this.$page.setTitleBar({ text: this.title })
    this.firstLoading = true
    this.getData().finally(() => {
      this.firstLoading = false
    })
  },

  getData() {
    return getBookStore().then(res => {
      if (res) {
        this.dataList = res.comicList
        this.currentTotalPages = 1
      }
    })
  },
  handlePulluprefresh() {
    console.log('上拉')
    if (this.loadMoreEnabled) {
      this.pulluprefreshing = true
      this.currentPage += 1
      this.getData().finally(() => {
        this.pulluprefreshing = false
      })
    } else {
      // this.$element('refresh').stopPullUpRefresh()
      this.pulluprefreshing = false
    }
  },
  handlePulldownrefresh() {
    console.log('下拉')
    this.pulldownrefreshing = true
    this.currentPage = 1
    this.getData().finally(() => {
      this.pulldownrefreshing = false
    })
  },

  addDesk() {
    shortcut.install({
      success: () => {
        console.log('成功。。。。')
        this.showShortcutBtn = false
      },
      fail: (data, code) => {
        console.log('失败。。。')
        this.showShortcutBtn = true
        console.log(`handling fail, code = ${code}, errorMsg=${data}`)
      },
    })
  },
  toRead(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
  back() {
    router.back()
  },
})
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.header {
  height: 88px;
  align-items: center;
  padding: 0 76px;
  &__back {
    position: absolute;
    left: 32px;
    width: 44px;
    height: 44px;
  }
  &__title {
    flex: 1;
    text-align: center;
    font-size: 36px;
    font-weight: bolder;
    color: #000000;
  }
}

.refresh {
  flex: 1;
}

.list {
  flex: 1;
  padding: 20px 32px;
  //background-color: #ffffff;

  .item {
    border-radius: 16px;
    margin-top: 48px;

    .img {
      width: 196px;
      height: 264px;
      margin-right: 24px;
      border: 2px solid #000000;
    }

    .info {
      flex: 1;
      flex-direction: column;
      padding-top: 24px;

      .title {
        font-size: 32px;
        font-weight: bolder;
        color: #292938;
        line-height: 32px;
        lines: 1;
        text-overflow: ellipsis;
      }

      .desc {
        font-size: 28px;
        color: #292938;
        line-height: 42px;
        margin-top: 16px;
        margin-bottom: 24px;
        lines: 2;
        text-overflow: ellipsis;
      }

      .type {
        margin-right: 20px;
      }

      .chapter,
      .type {
        font-size: 24px;
        line-height: 24px;
        lines: 1;
        text-overflow: ellipsis;
        color: #d1d1d1;
      }

      .chapter {
        //color: #19b5ff;
      }

      .type {
        //color: #b4b4b4;
      }
    }
  }
}

.loading-box {
  height: 130px;
  width: 100%;
  justify-content: center;
  align-items: center;
  /* background-color: #faebd7; */
  .loading {
    color: rgba(0, 0, 0, 0.3);
    /* font-size: 22px; */
  }
}

.add-desk {
  position: fixed;
  bottom: 76px;
  left: 175px;
}

.no-data {
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  text {
    margin-top: 40px;
  }
}
</style>
