import { storage } from '@quickapp/utils'

// 已经阅读的小说
const HAS_READ_NOVEL_KEY = 'HAS_READ_NOVEL_KEY'
// 已经阅读的小说章节
const HAS_READ_NOVEL_CHAPTER_KEY = 'HAS_READ_NOVEL_CHAPTER_KEY'
// 上次阅读的小说章节
const NOVEL_CHAPTER_KYE = 'NOVEL_CHAPTER_KYE'

/**
 * 获取小说上次阅读的章节
 * @returns {Promise | Promise<unknown>}
 */
export function getLastTimeNNovelChapter() {
  return storage.get(NOVEL_CHAPTER_KYE)
}

/**
 * 缓存小说上次阅读的章节
 * @param bookId {number} 小说id
 * @param chapterId {number} 章节id
 * @param chapterName {string} 章节标题
 */
export function setLastTimeNovelChapter({ bookId, chapterId, chapterName }) {
  return getLastTimeNNovelChapter().then(data => {
    const newData = {
      [bookId]: {
        chapterId,
        chapterName,
      },
    }

    const resData = {
      ...data,
      ...newData,
    }

    return storage.set(NOVEL_CHAPTER_KYE, data ? resData : newData)
  })
}

/**
 * 获取缓存已经阅读过的小说章节
 * @returns {Promise<unknown>}
 */
export function getHasReadNovelChapter() {
  return storage.get(HAS_READ_NOVEL_CHAPTER_KEY)
}

/**
 * 缓存已经阅读过的小说章节
 * @param bookId 小说id
 * @param chapterId 章节id
 * @param chapterName 章节标题
 */
export function setHasReadNovelChapter({ bookId, chapterId, chapterName }) {
  return getHasReadNovelChapter().then(data => {
    const setFirstData = () =>
      storage.set(HAS_READ_NOVEL_CHAPTER_KEY, {
        [bookId]: [{ chapterId, chapterName }],
      })

    if (data) {
      const bookData = data[bookId]

      if (bookData) {
        if (!bookData.find(it => it.chapterId === chapterId)) {
          bookData.push({ chapterId, chapterName })
        }
        return storage.set(HAS_READ_NOVEL_CHAPTER_KEY, {
          ...data,
          [bookId]: bookData,
        })
      }

      return storage.set(HAS_READ_NOVEL_CHAPTER_KEY, {
        ...data,
        [bookId]: [{ chapterId, chapterName }],
      })
    }

    return setFirstData()
  })
}

/**
 * 获取已经阅读过的小说
 * @returns {Promise | Promise<unknown>}
 */
export function getHasReadNovel() {
  return storage.get(HAS_READ_NOVEL_KEY)
}

/**
 * 缓存已经阅读过的小说
 */
export function setHasReadNovel({
  bookId,
  chapterId,
  chapterName,
  content,
  nextChapterId,
  preChapterId,
  name,
}) {
  getHasReadNovel().then(list => {
    if (list) {
      if (!list.find(it => it.bookId === bookId)) {
        list.push({
          bookId,
          chapterId,
          chapterName,
          content,
          nextChapterId,
          preChapterId,
          name,
        })
      }
      storage.set(HAS_READ_NOVEL_KEY, list)
    } else {
      storage.set(HAS_READ_NOVEL_KEY, [
        {
          bookId,
          chapterId,
          chapterName,
          content,
          nextChapterId,
          preChapterId,
          name,
        },
      ])
    }
  })
}
