<template>
  <list id="list" class="list-box" @scrollbottom="handleScrollBottom">
    <list-item class="item" type="list-item">
      <text class="title">目录</text>
    </list-item>
    <list-item
      @click="handleClick(item, $idx)"
      class="item"
      type="list-item"
      for="item in list"
      tid="$idx"
    >
      <text class="text {{item.disabled ? 'disabled': ''}}">
        {{ item.name }}
      </text>
      <image
        show="{{item.isLock}}"
        class="ic-lock"
        src="/assets/images/ic_lock.png"
      ></image>
    </list-item>
  </list>
</template>

<script>
import { getBookCatalogData } from '@/api/comics'
import { getHasReadNovelChapter } from '@/helper/business'

export default {
  data: {
    // list: new Array(0).fill({
    //   // isLock: true,
    //   // bookId: 2,
    //   // chapterOrder: 1,
    //   // id: 1461,
    //   // name: "正文卷 第1章 宋景山与黑妞",
    //   // words: 2414,
    // }),
    list: [],
    pageNum: 1,
    pageSize: 40,
    pageTotal: Number.MAX_VALUE,
    id: null,
    loading: false,
    // 已经阅读过的章节
    hasReadList: [],
    cacheIndex: 0,
  },

  computed: {
    enabledLoadMore() {
      return this.pageNum < this.pageTotal
    },
  },

  handleClick(item, index) {
    this.cacheIndex = index
    this.$emit('clickItem', { data: item })
  },

  getData(pageNum = this.pageNum, pageSize = this.pageSize) {
    if (this.enabledLoadMore) {
      if (this.loading) return

      this.loading = true

      return getBookCatalogData({ id: this.id, pageNum, size: pageSize })
        .then(res => {
          if (res) {
            this.list = this.list.concat(res.records)
            this.handleDisabledClassName()
          }
        })
        .finally(() => {
          this.loading = false
        })
    }
  },

  handleDisabledClassName() {
    getHasReadNovelChapter().then(list => {
      if (list && list[this.id]) {
        this.hasReadList = list[this.id]
        this.list = this.list.map(item => {
          return {
            ...item,
            disabled: !!this.hasReadList.find(it => {
              return it.chapterId === item.id
            }),
          }
        })
      }
    })
  },

  async initData(id) {
    this.id = id
    this.list = []

    this.getData(1, this.pageNum * this.pageSize).then(() => {
      setTimeout(() => {
        this.$element('list').scrollTo({
          index: this.cacheIndex,
        })
      }, 0)
    })
  },

  handleScrollBottom() {
    if (this.enabledLoadMore) {
      this.pageNum++
      this.getData()
    }
  },
}
</script>

<style lang="less">
.list-box {
  padding: 64px 50px 0;
  background-color: rgba(232, 224, 209, 1);
}

.title {
  font-size: 36px;
  font-weight: bolder;
  color: #38270c;
}

.item {
  padding: 30px 0;
  border-bottom: 2px solid rgba(56, 39, 12, 0.03);
  align-items: center;

  .ic-lock {
    width: 40px;
    height: 40px;
    margin-left: auto;
  }
}

.text {
  font-size: 28px;
  color: rgba(56, 39, 12, 0.5);
  line-height: 42px;
  margin-right: 15px;
}

.disabled {
  color: #585858;
}

.active {
  .text {
    color: rgba(148, 107, 30, 1);
  }
}
</style>
