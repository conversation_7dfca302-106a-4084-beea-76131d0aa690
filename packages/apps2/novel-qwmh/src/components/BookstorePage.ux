<import name="list-view" src="./ListView.ux"></import>

<template>
  <div class="wrapper">
    <div class="swiper-box">
      <swiper
        class="swiper"
        autoplay="true"
        index="{{swiperOpt.index}}"
        interval="{{swiperOpt.interval}}"
        indicator="{{swiperOpt.indicator}}"
      >
        <block if="{{hotRecommend && hotRecommend.length}}">
          <div class="swiper__item" for="item in hotRecommend" tid="$idx">
            <image
              @click="handleSwiperClick(item)"
              class="header-img"
              src="{{item.cover}}"
            ></image>
            <!--<div class="swiper__item__info">-->
            <!--  <text class="swiper__item__info__name">{{ item.name }}</text>-->
            <!--  <text class="swiper__item__info__chapter">{{ item.name }}</text>-->
            <!--</div>-->
          </div>
        </block>
        <text else class="swiper__default">{{ appName }}</text>
      </swiper>
    </div>

    <div class="top-box">
      <list class="top-list">
        <list-item
          type="item"
          class="top-list__item"
          for="item in recommend"
          tid="$idx"
          @click="toIntroduce(item)"
        >
          <image class="top-list__item__cover" src="{{item.cover}}"></image>
          <text class="top-list__item__name">{{ item.name }}</text>
          <text class="top-list__item__category">{{ item.category }}</text>
          <image
            class="top-list__item__tag"
            src="/assets/images/tag_jin.png"
          ></image>
        </list-item>
      </list>
    </div>

    <div class="hot-list">
      <div class="hot-list__header">
        <text class="hot-list__header__title">
          <span style="color: #ff005f">热门</span>
          <span>漫画</span>
        </text>
        <div class="hot-list__header__right" @click="toListPage">
          <text class="hot-list__header__right__more">更多</text>
          <image
            class="hot-list__header__right__icon-arrow"
            src="/assets/images/ic_entry.webp"
          ></image>
        </div>
      </div>
      <list-view list="{{comicList}}"></list-view>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookStore } from '@/api/comics'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      recommend: [
        {
          id: 1,
          cover: '/assets/images/logo.png',
          name: '开局地摊卖大力1',
          introduction:
            '地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代',
        },
      ],
      hotRecommend: [],
      comicList: [],
      swiperOpt: {
        index: 0,
        interval: 5000,
        indicator: false,
      },
      appName: __MANIFEST__.name,
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.handleChangeIndex()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      getBookStore().then(res => {
        this.recommend = res.recommend
        this.hotRecommend = res.hotRecommend
        this.comicList = res.comicList
      })
    }
  },
  toListPage() {
    router.push({
      uri: 'pages/List',
    })
  },
  toIntroduce(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
  handleSwiperClick(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40px;
}

.swiper-box {
  width: 100%;
  justify-content: center;
  align-items: center;
  //height: 536px;
  //background-image: url("/assets/images/s_bg.png");
  background-size: 100%;
}

.swiper {
  //width: 654px;
  height: 450px;

  &__item {
    flex-direction: column;
    background-color: white;

    &__info {
      padding: 0 10px;
      justify-content: space-between;
      align-items: center;

      &__name {
        margin: 10px;
        color: #000;
      }

      &__chapter {
        font-size: 24px;
        color: #b4b4b4;
      }
    }
  }

  .header-img {
    width: 100%;
    height: 100%;
  }

  &__default {
    font-size: 66px;
    text-align: center;
    font-weight: bold;
  }
}

.top-box {
  background-color: #ffffff;
  margin-top: 64px;

  .top-list {
    width: 100%;
    height: 360px;
    flex-direction: row;
    justify-content: space-between;
    box-sizing: border-box;
    padding-right: 48px;

    &__item {
      width: 240px;
      //flex: 1;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      margin-left: 48px;
      border-radius: 32px;

      &__tag {
        position: absolute;
        left: 0;
        top: 0;
      }

      &__cover {
        width: 100%;
        height: 320px;
        border: 2px solid #000;
      }

      text {
        line-height: 36px;
        text-overflow: ellipsis;
        lines: 1;
      }

      &__name {
        width: 100%;
        font-size: 28px;
        height: 48px;
        padding: 0 8px;
        margin-top: -46px;
        background-color: white;
        font-weight: bolder;
        color: #000;
        border: 2px solid #000;
      }

      &__category {
        align-self: flex-start;
        font-size: 24px;
        color: rgba(209, 209, 209, 1);
        line-height: 34px;
        padding-top: 16px;
      }
    }
  }
}

.hot-list {
  width: 100%;
  flex-direction: column;
  margin-top: 64px;
  padding-left: 48px;

  &__header {
    justify-content: space-between;
    margin-bottom: 34px;
    align-items: center;
    padding-right: 48px;

    &__title {
      font-size: 40px;
      font-weight: bold;
      color: #000;
      line-height: 44px;
    }

    &__right {
      width: 108px;
      height: 44px;
      background-color: #f1f0f4;
      border-radius: 22px;
      justify-content: center;
      align-items: center;

      &__more {
        color: black;
        font-size: 24px;
      }

      &__icon-arrow {
        width: 32px;
        height: 32px;
      }
    }
  }
}
</style>
