<template>
  <div class="chapter">
    <div class="chapter__header">
      <text class="chapter__header__title">全部章节</text>
      <text
        class="chapter__header__sort-up {{sortType === 'up' ? 'active' : 'disabled'}}"
        @click="handleSort('up')"
      >
        正序
      </text>
      <text
        class="chapter__header__sort-down {{sortType === 'down' ? 'active' : 'disabled'}}"
        @click="handleSort('down')"
      >
        倒序
      </text>
    </div>
    <div class="chapter__list">
      <div
        class="chapter__list__item"
        for="item in list"
        tid="$idx"
        @click="toDetail(item)"
      >
        <image
          class="chapter__list__item__cover"
          src="{{item.chapterCover}}"
        ></image>
        <text class="chapter__list__item__name">{{ item.name }}</text>
      </div>
    </div>
  </div>
</template>

<script>
import { getBookCatalogData } from '@/api/comics'

export default {
  props: {
    bookId: {
      type: Number,
    },
  },

  data() {
    return {
      list: new Array(10).fill({
        bookId: 1,
        chapterCover:
          'http://img.manhuadao.cn/bookcenter/176415/2201386/15114747922166406_chaptercover.jpg',
        chapterOrder: 1,
        createTime: '2022-09-08 17:19:37',
        id: 1,
        name: '第1话 汤',
        sourceId: '2201386',
        updateTime: '2022-09-08 17:19:37',
      }),
      sortType: 'up',
    }
  },

  onInit() {
    if (this.bookId) {
      getBookCatalogData({
        id: this.bookId,
        pageNum: 1,
        size: 100,
      }).then(res => {
        console.log(res)
        if (res && res.records) {
          this.list = res.records
        }
      })
    }
  },

  handleSort(type) {
    this.sortType = type
    this.list = this.list.sort((a, b) => {
      return type === 'up'
        ? a.chapterOrder - b.chapterOrder
        : b.chapterOrder - a.chapterOrder
    })
  },

  toDetail(item) {
    this.$emit('clickItem', item)
  },
}
</script>

<style lang="less">
.chapter {
  width: 100%;
  flex-direction: column;

  &__header {
    //border-bottom: 1px solid #ccc;
    padding: 28px 0;

    &__title {
      font-size: 32px;
      font-weight: bold;
      color: #333333;
    }

    &__sort-up {
      margin-left: auto;
    }

    &__sort-up,
    &__sort-down {
      font-size: 24px;
      line-height: 28px;
      width: 80px;
      height: 50px;
      text-align: center;
    }

    .active {
      border-radius: 26px;
      background-color: #f1f0f4;
      color: #000000;
    }

    .disabled {
      color: #b4b4b4;
    }
  }

  &__list {
    flex-direction: column;

    &__item {
      margin-top: 30px;

      &__cover {
        width: 136px;
        height: 136px;
        border-radius: 16px;
      }

      &__name {
        font-size: 32px;
        color: #333333;
        margin-left: 24px;
        font-weight: bold;
        lines: 3;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
