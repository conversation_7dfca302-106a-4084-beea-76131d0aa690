<template>
  <div class="list">
    <div class="item" for="item in list" tid="$idx" @click="toIntroduce(item)">
      <image class="item__cover" src="{{item.cover}}"></image>
      <div class="item__info">
        <text class="item__info__title">{{ item.name }}</text>
        <!--<text class="item__info__introduction">{{ item.introduction }}</text>-->
        <div class="item__info__other">
          <text class="item__info__other__type">{{ item.category }}</text>
          <!--<text class="item__info__other__chapter">-->
          <!--  {{ item.chapterCount }}卷-->
          <!--</text>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'

export default {
  props: {
    list: {
      type: Array,
      default: () => [
        {
          id: 4,
          cover: '/assets/images/2.webp',
          introduction:
            '海鸥学园流传着七大不可思议的奇妙传言。旧校舍3楼的女生厕所的第...',
          name: '九星霸体2222221九星霸体诀1222222',
          category: '热血',
          chapterCount: 12,
        },
      ],
    },
  },
  toIntroduce(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.list {
  width: 100%;
  //flex-direction: column;
  flex-wrap: wrap;

  .item {
    width: 202px;
    margin: 0 24px 32px 0;
    flex-direction: column;

    &__cover {
      width: 100%;
      height: 268px;
      border: 2px solid #000000;
    }

    &__info {
      flex: 1;
      flex-direction: column;
      padding-top: 12px;
      padding-bottom: 14px;
      box-sizing: border-box;

      &__title {
        font-size: 32px;
        font-weight: bold;
        color: #333;
        lines: 1;
        text-overflow: ellipsis;
      }

      &__introduction {
        font-size: 28px;
        margin-top: 12px;
        color: #999999;
        line-height: 42px;
        lines: 2;
        text-overflow: ellipsis;
      }

      &__other {
        margin-top: auto;

        text {
          font-size: 24px;
        }

        &__type {
          color: #d1d1d1;
        }

        &__chapter {
          color: #b4b4b4;
        }
      }
    }
  }
}
</style>
