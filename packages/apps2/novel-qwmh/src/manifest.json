{"id": "10123", "package": "com.qwyd.comic", "configCode": "cf_20230109114823", "name": "青蛙漫画", "versionName": "1.0.10", "versionCode": 11, "privacyUrl": "https://app-h5.springtool.cn/qwyd/agreement/privacy.html", "userUrl": "https://app-h5.springtool.cn/qwyd/agreement/user.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/qwyd/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/11503521/83b7", "serviceUrl": "https://app-h5.springtool.cn/static/views/service/novel.html", "minPlatformVersion": 1080, "icon": "/assets/images/logo.webp", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "template/official": "demo-template", "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Home": {"component": "index"}, "pages/Web": {"component": "index"}, "pages/List": {"component": "index"}, "pages/Service": {"component": "index"}, "pages/Content/Introduce": {"component": "index"}, "pages/Content/Detail": {"component": "index"}, "pages/Content/Reader": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "themeMode": 0, "menuBarData": {"menuBar": false}, "menu": true, "pages": {"pages/Home": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "menuBarData": {"menuBar": false}, "fullScreen": false}, "pages/Content/Introduce": {"titleBarText": "简介", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menuBarData": {"menuBar": false}, "menu": true}, "pages/Content/Detail": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Content/Reader": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": true}, "pages/Web": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "menu": true}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "pages/Service": {"titleBarText": "在线客服", "menu": true}, "pages/List": {"titleBarBackgroundColor": "#ffffff", "titleBarTextColor": "#414141", "titleBar": false, "menu": true}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}}}}