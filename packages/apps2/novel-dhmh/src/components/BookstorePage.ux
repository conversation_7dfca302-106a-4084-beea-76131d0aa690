<import name="list-view" src="./ListView.ux"></import>

<template>
  <div class="wrapper">
    <div class="swiper-box">
      <swiper
        class="swiper"
        autoplay="true"
        index="{{swiperOpt.index}}"
        interval="{{swiperOpt.interval}}"
        indicator="{{swiperOpt.indicator}}"
      >
        <block if="{{hotRecommend && hotRecommend.length}}">
          <div class="swiper__item" for="item in hotRecommend" tid="$idx">
            <image
              @click="handleSwiperClick(item)"
              class="header-img"
              src="{{item.cover}}"
            ></image>
            <div class="swiper__item__info">
              <text class="swiper__item__info__name">{{ item.name }}</text>
              <text class="swiper__item__info__chapter">
                更新至{{ item.chapterCount }}话
              </text>
            </div>
          </div>
        </block>
        <text else class="swiper__default">{{ appName }}</text>
      </swiper>
    </div>

    <div class="top-box">
      <div class="top-list">
        <div
          class="top-list__item"
          if="$idx < 3"
          for="item in recommend"
          tid="$idx"
          @click="toIntroduce(item)"
        >
          <image src="{{item.cover}}"></image>
          <text class="top-list__item__name">{{ item.name }}</text>
          <text class="top-list__item__category">{{ item.category }}</text>
        </div>
      </div>
    </div>

    <div class="hot-list">
      <div class="hot-list__header">
        <text class="hot-list__header__title">热门推荐</text>
        <div @click="toListPage">
          <text class="hot-list__header__more">更多</text>
          <image
            class="hot-list__header__icon-arrow"
            src="/assets/images/ic_arrow_right.webp"
          ></image>
        </div>
      </div>
      <list-view list="{{comicList}}"></list-view>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookStore } from '@/api/comics'

export default {
  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
  },

  data() {
    return {
      recommend: [
        {
          id: 1,
          cover: '/assets/images/logo.png',
          name: '开局地摊卖大力1',
          introduction:
            '地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代地复苏时代',
        },
      ],
      hotRecommend: [],
      comicList: [],
      swiperOpt: {
        index: 0,
        interval: 5000,
        indicator: false,
      },
      appName: __MANIFEST__.name,
    }
  },

  computed: {
    appName() {
      return __MANIFEST__.name
    },
  },

  onInit() {
    this.handleChangeIndex()
    this.$watch('currentIndex', 'handleChangeIndex')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      getBookStore().then(res => {
        this.recommend = res.recommend
        this.hotRecommend = res.hotRecommend
        this.comicList = res.comicList
        console.log('this.recommend')
        console.log(JSON.stringify(this.recommend))
      })
    }
  },
  toListPage() {
    router.push({
      uri: 'pages/List',
    })
  },
  toIntroduce(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
  handleSwiperClick(item) {
    router.push({
      uri: 'pages/Content/Introduce',
      params: {
        id: item.id,
      },
    })
  },
}
</script>

<style lang="less">
.wrapper {
  width: 100%;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40px;
}

.swiper-box {
  width: 100%;
  justify-content: center;
  align-items: center;
  height: 536px;
  background-image: url('/assets/images/s_bg.png');
  background-size: 100%;
}

.swiper {
  width: 654px;
  height: 384px;

  &__item {
    flex-direction: column;
    background-color: white;
    padding: 10px;
    border-radius: 24px;

    &__info {
      padding: 0 10px;
      justify-content: space-between;
      align-items: center;

      &__name {
        margin: 10px;
        color: #000;
      }

      &__chapter {
        font-size: 24px;
        color: #b4b4b4;
      }
    }
  }

  .header-img {
    width: 100%;
    height: 350px;
    border-radius: 24px;
  }

  &__default {
    font-size: 66px;
    text-align: center;
    font-weight: bold;
  }
}

.top-box {
  background-color: #ffffff;
  padding: 0 30px;

  .top-list {
    width: 100%;
    justify-content: space-between;
    padding: 32px 17px;
    box-sizing: border-box;

    &__item {
      flex: 1;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      margin: 0 8px;
      border-radius: 32px;

      image {
        width: 100%;
        height: 264px;
        border-radius: 32px;
      }

      text {
        width: 100%;
        margin-top: 10px;
        line-height: 36px;
        text-overflow: ellipsis;
        lines: 1;
      }

      &__name {
        font-size: 28px;
        font-weight: bolder;
        color: #000000;
      }

      &__category {
        font-size: 24px;
        color: #19b5ff;
        line-height: 34px;
      }
    }
  }
}

.hot-list {
  width: 100%;
  flex-direction: column;
  border-radius: 20px;
  background-color: #ffffff;
  margin-top: 20px;
  padding: 0 40px;

  &__header {
    justify-content: space-between;
    margin-bottom: 34px;
    align-items: center;

    &__title {
      font-size: 40px;
      font-weight: bold;
      color: #000;
      line-height: 44px;
    }

    &__more {
      margin-right: -15px;
    }

    &__icon-arrow {
      width: 40px;
      height: 40px;
    }
  }
}
</style>
