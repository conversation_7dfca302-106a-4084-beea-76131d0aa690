import { request } from '@quickapp/business/lib/config'

/**
 * 漫画介绍页
 * @returns {Promise | Promise<unknown>}
 */
export function getBookInfoData({ id }) {
  return request({
    method: 'GET',
    url: '/comic/book/info',
    data: {
      id,
    },
  })
}

/**
 * 漫画目录
 * @returns {*}
 */
export function getBookCatalogData({ id, pageNum, size }) {
  return request({
    method: 'GET',
    url: '/comic/book/catalog',
    data: {
      id,
      pageNum,
      size,
    },
  })
}

/**
 * 章节内容
 * @returns {Promise | Promise<unknown>}
 */
export function getChapterContentData({ bookId, chapterId }) {
  return request({
    method: 'GET',
    url: '/comic/book/chapterContent',
    data: { bookId, chapterId },
  })
}

/**
 * 解锁
 * @param bookId {number}
 * @param chapterId {number}
 * @returns {Promise | Promise<unknown>}
 */
export function unlockChapter({ bookId, chapterId }) {
  return request({
    method: 'POST',
    url: '/comic/book/unlockChapter',
    data: { bookId, chapterId },
  })
}

/**
 * 书城
 * @returns {Promise | Promise<unknown>}
 */
export function getBookStore() {
  return request({
    method: 'GET',
    url: '/comic/bookStore',
  })
}

/**
 * 书架
 * @returns {Promise | Promise<unknown>}
 */
export function getBookShelf() {
  return request({
    method: 'GET',
    url: '/comic/bookshelf',
  })
}

/**
 * 加入书架
 * @param idList {number[]}
 * @returns {Promise | Promise<unknown>}
 */
export function addBookShelf(idList) {
  return request({
    method: 'POST',
    url: '/comic/bookshelf/add',
    data: {
      idList,
    },
  })
}

/**
 * 移除书架
 * @param idList {number[]}
 * @returns {Promise | Promise<unknown>}
 */
export function removeBookShelf(idList) {
  return request({
    method: 'POST',
    url: '/comic/bookshelf/remove',
    data: {
      idList,
    },
  })
}
