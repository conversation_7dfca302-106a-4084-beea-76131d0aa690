<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import name="apex-popup" src="@quickapp/apex-ui/components/popup/index"></import>
<import name="chapter-list" src="../../../components/chapter-list"></import>

<template>
  <div id="drawer" class="page">
    <image
      class="back-btn"
      src="/assets/images/ic_back.png"
      @click="back"
    ></image>
    <image class="cover" src="{{info.cover}}"></image>

    <div class="info-box">
      <div class="title-box">
        <text class="title">{{ info.name }}</text>
        <div class="chapter__category">
          <text class="category">{{ info.category }}</text>
          <text class="chapter">{{ info.chapterCount }}卷</text>
        </div>
      </div>
    </div>
    <input
      show="{{showAddBookShelf}}"
      class="add-bookshelf"
      type="button"
      value="加入书架"
      @click="addBookShelf"
    />
    <text class="introduction">{{ info.introduction }}</text>

    <div class="ad-box">
      <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
    </div>

    <div class="chapter-box">
      <chapter-list
        book-id="{{id}}"
        @click-item="handleClickItem"
      ></chapter-list>
    </div>
    <div class="footer" @click="toDetail">
      <!-- <input
        show="{{showAddBookShelf}}"
        class="add-bookshelf"
        type="button"
        value="加入书架"
        @click="addBookShelf"
      /> -->
      <text>马上阅读</text>
      <!-- <input class="read" type="button" value="马上阅读" @click="toDetail" /> -->
    </div>
  </div>
</template>

<script>
import { addBookShelf, getBookInfoData } from '@/api/comics'
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
      isInShelf: false,
      chapterId: null, // 读到到章节
    },
    showDraw: false,
    showAddBookShelf: true,
  },

  protected: {
    id: 1, // 小说id
  },

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) { }
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
        this.showAddBookShelf = !res.isInShelf
      }
    })
  },

  toDetail() {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: this.id,
        chapterId: this.info.chapterId || this.info.firstChapterId,
      },
    })
  },

  handleClickItem({ detail }) {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.bookId,
        chapterId: detail.id,
      },
    })
  },

  addBookShelf() {
    addBookShelf([this.info.id])
      .then(() => {
        showToast('成功加入书架')
        this.showAddBookShelf = false
      })
      .catch(() => {
        showToast('失败加入书架')
      })
  },

  back() {
    router.back()
  },
})
</script>

<style lang="less">
.page {
  flex-direction: column;
  padding: 0 0 188px;
}

.back-btn {
  position: fixed;
  left: 40px;
  top: 118px;
  width: 44px;
}

.cover {
  width: 100%;
  height: 514px;
}

#drawer {
  position: relative;
  .add-bookshelf {
    width: 96px;
    height: 84px;
    left: 500px;
    top: -50px;
    width: 218px;
    background-color: #ffce00;
    border-radius: 12px;
    padding: 10px 24px;
    /* margin-right: 24px; */
  }
}

.ad-box {
  border-radius: 20px;
  margin-top: 40px;
  justify-content: center;
}

.info-box {
  margin-top: -140px;
}

.title-box {
  padding: 0 40px;
  flex-direction: column;
  margin-top: 24px;
  margin-bottom: 30px;

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #ffffff;
  }
  .chapter__category{
    display: flex;
    justify-content: space-between;
  }
  .category {
    text-align: center;
    height: 44px;
    font-size: 28px;
    color: #19b5ff;
  }
  .chapter{
    color: #fff;
  }
}

.introduction {
  font-size: 28px;
  color: rgba(41, 41, 56, 0.69);
  line-height: 42px;
  background-color: white;
  padding: 24px;
  margin: 40px 40px 0;
  border-radius: 32px;
  border: 2px solid #e4e7e1;
}

.chapter-box {
  background-color: white;
  padding: 0 32px;
  border: 1px solid #e4e7e1;
  border-radius: 16px;
  margin: 0 48px;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750px;
  height: 148px;
  /* padding-top: 32px; */
  padding-right: 32px;
  padding-left: 32px;
  justify-content: center;
  background-color: #ffce00;
  box-shadow: 0px -2px 6px 0px #e8e8e8;
  backdrop-filter: blur(10px);
  border-top: 1px solid #eee;
  color: #000;
  text {
    font-size: 38px;
    color: #000;
  }

  input {
    text-align: center;
    height: 84px;
    font-size: 32px;
    color: #000;
    /* border-radius: 12px; */
  }

  .add-bookshelf {
    width: 218px;
    background: linear-gradient(270deg, #ff9c07 0%, #ff7010 100%);
    border-radius: 24px;
    padding: 0 24px;
    margin-right: 24px;
  }

  .read {
    flex: 1;
    /* background: linear-gradient(90deg, #ff5018 0%, #ff1741 100%); */
    background-color: #ffce00;

    /* border-radius: 24px; */
  }
}
</style>
