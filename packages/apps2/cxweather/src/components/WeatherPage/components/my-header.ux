<template>
  <div class="header">
    <div class="address" @click="toCityPage">
      <image class="ic-plus" src="/assets/images/ic_plus.png"></image>
      <text class="tv-address">
        {{ locationInfo.district }} {{ locationInfo.street }}
      </text>
      <image class="ic-address" src="/assets/images/ic_address.png"></image>
    </div>
    <!--不能0px;不能display:none-->
    <video
      style="width: 1px; height: 1px"
      id="video"
      @pause="handlePauseEvt"
      @start="handleStartEvt"
      @finish="handleFinisEvt"
      src="{{weatherNow.audioUrl}}"
    ></video>

    <div class="temperature">
      
      <div class="num-box">
        <text class="num">{{ weatherNow.temperature }}</text>

        <div class="unit"></div>
        <text class="status">C</text>
        <!-- <div class="ic-voice-box">
          <image
            class="ic-voice"
            @click="handleVoice"
            src="/assets/images/ic_voice.png"
          />
          <div if="{{showDot}}" class="dot"></div>
        </div> -->
      </div>
      <div class="icon">
        <weather-icon
          status="{{weatherNow.code}}"
          width="140px"
          height="140px"
        ></weather-icon>
      </div>
      <!-- <div class="warnUser">
        <div class="status-box">
          <image class="icon" src="/assets/images/leaves.png"></image>
          <text class="status-txt">{{ airNow.quality }} {{ airNow.aqi }}</text>
        </div>
        <div class="status-box warn-box"> 
          <image class="icon" src="/assets/images/warn.png"></image>
          <text class="status-txt">天气预警</text>
        </div>
        <div class="status-box">
          <image class="icon" src="/assets/images/taifeng.png"></image>
          <text class="status-txt">台风路径</text>
        </div>
      </div> -->
    </div>

    <div class="two-day">
      <swiper
        if="{{tipList && tipList.length > 0}}"
        class="swiper"
        indicator="{{false}}"
        vertical="{{true}}"
        autoplay="{{true}}"
        loop="{{true}}"
      >
        <div class="tip-box" for="item in tipList" tid="$idx">
          <image class="icon" src="/assets/images/ic_warn.png"></image>
          <text class="text">{{ item }}将会下雨</text>
        </div>
      </swiper>

      <div class="data-box">
        <div class="today">
          <div class="left">
            <text class="date-txt">今天</text>
            <div class="icon">
              <weather-icon
                status="{{weatherDaily[0] ? weatherDaily[0].code_day  : '0'}}"
                width="56px"
                height="56px"
              ></weather-icon>
            </div>
          </div>
          <div class="right">
            <text class="date">
              {{ weatherDaily[0] ? weatherDaily[0].low : '' }}/{{
                weatherDaily[0] ? weatherDaily[0].high : ''
              }}°
            </text>
            <text class="desc">
              {{
                handleTxt(
                  weatherDaily[0] ? weatherDaily[0].text_day : '',
                  weatherDaily[0] ? weatherDaily[0].text_night : ''
                )
              }}
            </text>
          </div>
        </div>
        <div class="line"></div>
        <div class="tomorrow">
          <div class="left">
            <text class="date-txt">明天</text>
            <div class="icon">
              <weather-icon
                status="{{weatherDaily[1] ? weatherDaily[1].code_day  : '0'}}"
                width="56px"
                height="56px"
              ></weather-icon>
            </div>
          </div>
          <div class="right">
            <text class="date">
              {{ weatherDaily[1] ? weatherDaily[1].low : '' }}/{{
                weatherDaily[1] ? weatherDaily[1].high : ''
              }}°
            </text>
            <text class="desc">
              {{
                handleTxt(
                  weatherDaily[1] ? weatherDaily[1].text_day : '',
                  weatherDaily[1] ? weatherDaily[1].text_night : ''
                )
              }}
            </text>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import ad from '@service.ad'

export default {
  props: {
    hourRainList: {
      type: Array,
      default: () => [],
    },
    weatherDaily: {
      type: Array,
      default: () => [],
    },
    locationInfo: {
      type: Object,
      default: () => ({
        province: '',
        city: '',
        district: '',
        street: '',
      }),
    },
    weatherNow: {
      type: Object,
      default: () => ({
        clouds: '0',
        code: '1',
        dew_point: '',
        feels_like: '27',
        humidity: '81',
        pressure: '1000',
        temperature: '24',
        text: '晴',
        time: null,
        visibility: '7.0',
        wind_direction: '西南',
        wind_direction_degree: '210',
        wind_scale: '1',
        wind_speed: '5.0',
        audioUrl: '',
      }),
    },
    airNow: {
      type: Object,
      default: () => ({
        aqi: '24',
        co: '0.542',
        last_update: '2022-07-20T22:00:00+08:00',
        no2: '19',
        o3: '42',
        pm10: '24',
        pm25: '12',
        primary_pollutant: '',
        quality: '优',
        so2: '6',
      }),
    },
  },

  data() {
    return {
      adData: null,
      isVideStart: false,
      showDot: true,
      isUnlock: false,
    }
  },

  computed: {
    tipList() {
      const addZero = n => (n < 10 ? `0${n}` : n)

      const list = this.hourRainList.map(it => {
        const date = new Date(it)
        const m = date.getMonth() + 1
        const d = date.getDate()
        const hours = date.getHours()
        const minutes = date.getMinutes()

        return `${m}月${d}日${addZero(hours)}:${addZero(minutes)}`
      })

      console.log('下雨列表', list)
      return list
    },
  },

  handleTxt(t1, t2) {
    return t1 === t2 ? t1 : `${t1}转${t2}`
  },

  onInit() {
    const provider = ad.getProvider()
    console.log('provider', provider)
    this.loadAd()
  },

  loadAd() {},

  toCityPage() {
    router.push({
      uri: 'pages/City',
    })
  },

  async handleVoice() {
    if (this.isUnlock) {
      this.handleVoiceInternal()
      return
    }
    if (this.adData != null) {
      const brand = ad.getProvider()
      if (brand.toLowerCase() === 'oppo') {
        showToast('即将进入激励视频')
        await this.delay(2000)
      }
      this.handleVoiceInternal()
      this.isUnlock = true
      this.loadAd()
    } else {
      this.handleVoiceInternal()
      this.loadAd()
    }
  },

  delay(t = 1000) {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve()
      }, t)
    })
  },

  handleVoiceInternal() {
    const video = this.$element('video')
    // const video = this.$child('video')
    this.showDot = false
    if (this.isVideStart) {
      video.pause()
    } else {
      video.start()
    }
  },

  handlePauseEvt() {
    this.isVideStart = false
  },

  handleStartEvt() {
    this.isVideStart = true
  },

  handleFinisEvt() {
    this.$element('video').setCurrentTime({ currenttime: 0 })
    this.isVideStart = false
  },
}
</script>

<style lang="less">
.header {
  flex-direction: column;
  width: 100%;
  height: 928px;
  /* background-image: url('/assets/images/home_header_bg.webp'); */
  background-size: cover;
  padding: 0 20px;
  padding-top: 104px;
  //background-size: 100%;
  /* background: linear-gradient(180deg, #ebefff 0%, #3860ff 100%); */
  background-color: #3860ff;
  &_icon &_address_info {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 14px;
  }
  .address {
    padding-left: 32px;

    .ic-plus,
    .ic-address {
      width: 48px;
    }

    .tv-address {
      margin: 0 8px;
      font-size: 36px;
      font-weight: bolder;
      color: #ffffff;
      /* color: #000000; */
      line-height: 48px;
    }
  }

  .temperature {
    display: flex;
    /* justify-content: center; */
    align-items: center;
    flex-direction: column;
    width: 430px;
    height: 430px;
    /* background-image: url('/assets/images/pin_icon.png'); */
    background-size: cover;
    background-repeat: no-repeat;
    border-radius: 40px;
    margin: 6px auto 0;
    margin-top: 69px;
    padding: 24px 38px;
    .icon {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .num-box {
      padding-right: 15px;
      margin-bottom: 60px;
      .num {
        height: 160px;
        font-size: 160px;
        font-weight: 600;
        color: #ffffff;
        line-height: 160px;
        margin-right: 12px;
      }

      .unit {
        width: 44px;
        height: 44px;
        border: 12px solid #ffffff;
        border-radius: 50%;
        margin-top: 32px;
        /* margin-left: -12px; */
      }

      .status {
        font-size: 66px;
        color: #ffffff;
        line-height: 56px;
        align-self: flex-end;
        margin-left: -46px;
      }

      .ic-voice-box {
        visibility: hidden;
        /* margin-left: auto; */
        align-self: flex-start;
        margin-top: 20px;
        .ic-voice {
          width: 64px;
          height: 64px;
        }

        .dot {
          width: 20px;
          height: 20px;
          background-color: red;
          border-radius: 50%;
          margin-left: -15px;
        }
      }
    }
    .status-box {
      justify-content: center;
      align-items: center;
      width: 188px;
      height: 64px;
      margin-top: 20px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 32px;
      margin-right: 20px;
      .icon {
        width: 56px;
        height: 56px;
      }
      .status-txt {
        font-size: 28px;
        font-weight: bold;
        color: #ffffff;
      }
    }
    .warn-box {
      background-color: #f75540;
    }
  }
  .two-day {
    width: 100%;
    flex-direction: column;
    padding: 32px 48px;
    /* background: linear-gradient(180deg, #ebefff 0%, #5769F0 100%); */
    /* background-color: #111111; */
    border-radius: 24px;
    /* border: 1px solid rgba(192, 202, 216, 0.2); */
    margin-top: 20px;

    .swiper {
      height: 88px;
      background-color: rgba(255, 222, 69, 0.1);
      border-radius: 16px;
    }

    .tip-box {
      height: 100%;
      padding: 0 12px;
      align-items: center;

      .icon {
        margin-right: 8px;
        width: 48px;
        height: 48px;
      }

      .text {
        font-size: 28px;
        font-weight: 400;
        color: #ffde45;
      }
    }

    .data-box {
      justify-content: space-between;
      margin-top: 28px;
      align-items: center;

      .today,
      .tomorrow {
        flex: 1;
        justify-content: space-between;
      }

      .today {
        padding-right: 32px;
      }

      .line {
        width: 2px;
        height: 76px;
        /* background-color: #F0F8FF; */
      }

      .tomorrow {
        padding-left: 32px;
      }

      .left,
      .right {
        align-items: center;
        flex-direction: column;
      }

      .date-txt {
        font-size: 32px;
        font-weight: 400;
        color: #f0f8ff;
        line-height: 44px;
      }

      .icon {
        width: 56px;
        height: 56px;
        margin-top: 16px;
      }

      .date {
        font-size: 44px;
        font-family: Bebas;
        color: #f0f8ff;
        line-height: 44px;
        /* margin-top: 8px; */
        font-weight: bolder;
      }

      .desc {
        font-size: 32px;
        font-weight: 400;
        color: #f0f8ff;
        line-height: 44px;
        margin-top: 16px;
      }
    }
  }
}
</style>
