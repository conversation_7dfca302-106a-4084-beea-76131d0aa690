<import
  name="weather-page"
  src="../../components/WeatherPage/index.ux"
></import>
<import
  name="mine-page"
  src="@quickapp/mc-ui/components/setting-page.ux"
></import>
<import
  name="billing-page"
  src="../../components/BillingPage/index.ux"
></import>

<!-- <import name="info-page" src="../../components/InfoPage.ux"></import> -->
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>

<template>
  <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
    <tab-content class="content">
      <div class="content-box" for="item in pageList" tid="$idx">
        <component
          id="{{item.pageComponent}}"
          is="{{item.pageComponent}}"
          index="{{$idx}}"
          current-index="{{currentIndex}}"
        ></component>
      </div>
    </tab-content>
    <tab-bar class="tab-bar">
      <div for="item in pageList" tid="$idx" class="tab-bar-item">
        <block if="{{item.pageComponent === 'book-keep-page'}}">
          <div class="center-tab">
            <image class="icon" src="/assets/images/ic_add.png"></image>
          </div>
          <text class="tab-text">{{ item.text }}</text>
        </block>
        <block else>
          <text class="tab-text">{{ item.text }}</text>
        </block>
      </div>
    </tab-bar>
    <service></service>
  </tabs>
</template>

<script>
import device from '@quickapp/business/lib/device'
import { trackEvent } from '@quickapp/business'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    pageList: [
      {
        pageComponent: 'weather-page',
        text: '天气',
      },
      {
        pageComponent: 'billing-page',
        text: '记账',
      },
      {
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
  },

  onInit() {
    // device.getDeviceInfo().then(res => {
    //   if (
    //     res &&
    //     res.platformVersionCode >= 1103 &&
    //     res.brand.toLowerCase() === 'oppo'
    //   ) {
    //     this.pageList.splice(1, 0, {
    //       iconPath: '/assets/images/ic_tab_info.png',
    //       selectedIconPath: '/assets/images/ic_tab_info_active.png',
    //       pageComponent: 'info-page',
    //       text: '内容',
    //     })
    //   }
    // })
  },

  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })

    const weatherPage = this.$child('weather-page')
    if (weatherPage) {
      weatherPage.getInitData()
    }
    const Billingpage = this.$child('billing-page')
    if (Billingpage) {
      Billingpage.getInitData()
    }
  },

  onBackPress() {
    return false
  },
  handleAdClose() {},

  handleChange(evt) {
    this.currentIndex = evt.index
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';

.wrapper {
  width: 100%;
  height: 100%;
  /* background-color: #ebefff; */
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  /* background-color: #ebefff; */
  border-top: 1px solid#323845;
}

.center-tab {
  justify-content: center;
  align-items: center;
  /* background-color: #ebefff; */
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;
  /* background-color: #ebefff; */

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 52px;
    height: 52px;
  }

  .tab-text {
    /* text-align: center; */
    font-size: 38px;
    color: #333;
    line-height: 100px;
  }

  .tab-text:active {
    color: #3860ff;
  }
}
</style>
