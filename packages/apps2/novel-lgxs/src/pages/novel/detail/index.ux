<import
  name="table-content"
  src="../../../components/table-content.ux"
></import>
<import
  name="novel-setting"
  src="../../../components/novel-setting.ux"
></import>
<import name="apx-spin" src="@quickapp/apex-ui/components/spin/index"></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import> -->
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<!-- <import
  name="nativebanner"
  src="@quickapp/business/lib/nativebanner.ux"
></import> -->
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>
<import
  name="dialog-unlock"
  src="../../../components/dialog-unlock.ux"
></import>
<import name="my-navbar" src="@quickapp/mc-ui/components/navbar.ux"></import>
<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div
    id="drawer"
    class="page"
    style="background-color: {{currentTheme.bgColor}}"
  >
    <!-- <intad
      if="showIntAd"
      @close="onIntAdClose"
      event-name="ad_essay_insert"
    ></intad> -->

    <my-navbar
      title="{{name}}"
      show-status-bar="{{true}}"
      fixed="true"
      bg-color="{{currentTheme.bgColor}}"
      title-color="{{currentTheme.frontColor}}"
      show-back="true"
      theme="{{currentTheme.theme}}"
    ></my-navbar>

    <apx-spin tip="加载中..." nested="{{true}}" loading="{{loading}}">
      <list slot="nested" id="list" class="wrapper" @click="handleClick">
        <!--<list-item class="name-box" type="list-name">-->
        <!--  <text class="name" style="color: {{currentTheme.frontColor}}">-->
        <!--    {{ name }}-->
        <!--  </text>-->
        <!--</list-item>-->
        <list-item class="chapter-item" type="list-item">
          <div class="header">
            <text class="title" style="color: {{currentTheme.frontColor}}">
              {{ chapter.chapterName }}
            </text>
          </div>
          <div class="content">
            <block for="item in chapterContent" tid="$idx">
              <text
                if="item.type === 'text'"
                class="content-text"
                style="{{contentTextStyle}}"
              >
                {{ item.content }}
              </text>
              <div class="text-ad-container" else>
                <!-- <nativead
                  id="nativead"
                  event-name="ad_essay"
                  show-ad-free="{{true}}"
                ></nativead> -->
              </div>
            </block>
          </div>
        </list-item>
        <list-item type="footer" class="page-footer">
          <text
            class="btn {{!chapter.preChapterId ? 'disabled' : ''}}"
            @click="handleFooterBtnPrev"
            style="color: {{currentTheme.frontColor}}"
          >
            上一章
          </text>
          <text
            class="btn"
            @click="handleFooterBtnTc"
            style="color: {{currentTheme.frontColor}}"
          >
            目录
          </text>
          <text
            class="btn {{!chapter.nextChapterId ? 'disabled' : ''}}"
            @click="handleFooterBtnNext"
            style="color: {{currentTheme.frontColor}}"
          >
            下一章
          </text>
        </list-item>
        <list-item @click="addDesk" type="add-desk" class="shortcut-box">
          <!--<short-cut-->
          <!--  width="650px"-->
          <!--  height="100px"-->
          <!--  bg-color="#e7543d"-->
          <!--  text="添加到桌面"-->
          <!--&gt;</short-cut>-->
        </list-item>
      </list>
    </apx-spin>

    <div if="showSetting" class="setting-box">
      <novel-setting
        @change-font="handleChangeFont"
        @change-theme="handleChangeColor"
        @click-table-content="openDrawer"
        @click-next="nextPageContent"
        @click-prev="prePageContent"
        font-size="{{currentFontSize}}"
        theme="{{currentTheme}}"
        has-next="{{!!chapter.nextChapterId}}"
        has-prev="{{!!chapter.preChapterId}}"
      ></novel-setting>
    </div>
    <apex-popup id="popup" position="left">
      <table-content
        id="table-content"
        @click-item="handleClickItem"
      ></table-content>
    </apex-popup>
    <div if="{{false}}" class="ad-container">
      <!-- <nativebanner event-name="ad_essay_bottom"></nativebanner> -->
    </div>
    <dialog-unlock
      if="{{showDialog}}"
      @close="handleCloseDialog"
      @unlock="handleUnlock"
      @shortcut="handleShortcut"
      has-shortcut="{{hasShortcut}}"
    ></dialog-unlock>
  </div>
</template>

<script>
import {
  defaultFontSize,
  defaultTheme,
  getNovelFontSize,
  getNovelTheme,
  setHasReadNovel,
  setHasReadNovelChapter,
  setLastTimeNovelChapter,
  setNovelFontSize,
  setNovelTheme,
} from '@/helper/business'
import { getChapterContentData, unlockChapter } from '@/api/bookInfo'
import utils from '@/helper/utils'
import { createChapterContent } from '@/helper/reader'

import { trackEvent } from '@quickapp/business'
import shortcut from '@system.shortcut'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const m_global = global.__proto__ || global

export default setPageMenuConfig({
  private: {
    showShortcutBtn: true,
    showSetting: false,
    showIntAd: false,
    currentFontSize: defaultFontSize,
    currentTheme: defaultTheme,
    currentChapter: 1,
    chapter: {
      chapterName: '',
      content: '',
      bookId: null,
      nextChapterId: null,
      preChapterId: null,
      chapterId: null,
    },
    loading: true,
    showDialog: false,
    showIntCount: 0,
    hasShortcut: false,
  },

  protected: {
    bookId: null,
    chapterId: null,
    name: '开局买大力',
  },

  computed: {
    contentTextStyle() {
      return {
        fontSize: this.currentFontSize + 'px',
        lineHeight: this.currentFontSize * 2.6 + 'px',
        color: this.currentTheme.frontColor,
      }
    },
    chapterContent() {
      if (!this.chapter || !this.chapter.content) {
        return []
      }
      const arr = createChapterContent(this.chapter.content).map(it => ({
        type: 'text',
        content: it,
      }))
      arr.splice(0, 0, {
        type: 'ad',
      })
      return arr
    },
  },

  onInit() {
    getNovelFontSize().then(fontSize => {
      this.setFontSize(fontSize)
    })

    getNovelTheme().then(theme => {
      this.setTheme(theme)
    })

    this.getData(this.chapterId)

    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'novel_watch',
      opt_value: this.bookId,
    })
  },

  onShow() {
    shortcut.hasInstalled({
      success: result => {
        this.hasShortcut = result
      },
    })

    const nativeAd = this.$child('nativead')
    if (nativeAd && nativeAd.onShow) {
      nativeAd.onShow()
    }
  },

  onBackPress() {
    this.setLastTimeNovelChapter().finally(() => {
      this.$page.finish()
    })
    return true
  },

  handleShortcut({ detail }) {
    this.hasShortcut = detail
  },

  addDesk(event) {
    event.stopPropagation()
  },

  setLastTimeNovelChapter() {
    return setLastTimeNovelChapter({
      bookId: this.chapter.bookId,
      chapterId: this.chapter.chapterId,
      chapterName: this.chapter.chapterName,
      introduction: this.chapter.introduction,
    })
  },

  setFontSize(fontSize) {
    this.currentFontSize = fontSize
    setNovelFontSize(fontSize)
  },

  setTheme(theme) {
    this.currentTheme = theme
    setNovelTheme(theme)
  },

  openDrawer() {
    this.showSetting = false
    this.$child('popup').show()
    this.$child('table-content').initData(this.chapter.bookId)
  },

  handleClickItem({ detail }) {
    this.$child('popup').hide()
    this.getData(detail.data.id, detail.data.bookId)
  },

  handleClick(evt) {
    const windowWidth = 750
    const clientX = evt.clientX
    const efficientAreaWidth = windowWidth / 3

    if (clientX > efficientAreaWidth && clientX < efficientAreaWidth * 2) {
      this.showSetting = !this.showSetting
    } else {
      this.showSetting = false
    }
  },

  handleChangeFont({ detail }) {
    this.setFontSize(detail.font)
  },

  handleChangeColor({ detail }) {
    this.setTheme(detail.theme)
  },

  handleClickNext(evt) {
    evt.stopPropagation()
  },

  getData(chapterId = this.chapterId, bookId = this.bookId) {
    this.loading = true
    getChapterContentData({
      bookId,
      chapterId,
    })
      .then(res => {
        if (res) {
          this.chapter = res

          this.showDialog = res.isLock
          this.$element('list').scrollTo({
            index: 0,
          })
        }

        setHasReadNovelChapter({
          bookId: this.chapter.bookId,
          chapterId: this.chapter.chapterId,
          chapterName: this.chapter.chapterName,
          introduction: this.chapter.introduction,
        })

        setHasReadNovel({
          ...res,
          name: this.name,
        })

        this.setLastTimeNovelChapter()

        trackEvent({
          category: 'page',
          action: 'show',
          opt_label: 'section_watch',
          opt_value: this.bookId,
        })
      })
      .finally(() => {
        this.loading = false
      })
  },

  nextPageContent() {
    if (this.chapter.nextChapterId) {
      this.getData(this.chapter.nextChapterId)
      this.showSetting = false
    } else {
      utils.showToast('看完了')
    }
  },

  prePageContent() {
    if (this.chapter.preChapterId) {
      this.getData(this.chapter.preChapterId)
      this.showSetting = false
    } else {
      utils.showToast('没有上一章了')
    }
  },

  handleFooterBtnTc(evt) {
    this.openDrawer()
    evt.stopPropagation()
  },

  handleFooterBtnNext(evt) {
    this.nextPageContent()
    evt.stopPropagation()
    if (this.showIntCount % 2 === 0) {
      this.showIntAd = true
    }
    this.showIntCount++
  },

  handleFooterBtnPrev(evt) {
    this.prePageContent()
    evt.stopPropagation()
  },

  onIntAdClose() {
    this.showIntAd = false
  },

  handleCloseDialog() {
    this.setLastTimeNovelChapter().finally(() => {
      this.$page.finish()
    })
  },

  handleUnlock() {
    unlockChapter({
      bookId: this.chapter.bookId,
      chapterId: this.chapter.chapterId,
    }).then(res => {
      if (res) {
        this.showDialog = false
      } else {
        utils.showToast('解锁失败，请重试')
      }
    })
  },

  handleChangeShow({ detail }) {
    this.showShortcutBtn = detail
  },
})
</script>

<style lang="less">
.page {
  background-color: rgba(246, 241, 231, 1);
  padding-top: 160px;
}

.wrapper {
  flex: 1;
  flex-direction: column;
  padding: 0 50px;
}

.chapter-item {
  flex-direction: column;
}

.name-box {
  padding-top: 40px;

  .name {
    font-size: 24px;
    font-weight: 400;
    color: #b1ab9f;
  }
}

.header {
  margin-top: 64px;

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #333333;
    line-height: 56px;
  }
}

.content {
  flex-direction: column;
  margin-top: 40px;

  .content-text {
    font-weight: 400;
    color: #38270c;
  }
}

.setting-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 440px;
}

.page-footer {
  justify-content: space-between;
  padding-top: 40px;
  padding-bottom: 80px;

  .btn {
    text-align: center;
    width: 150px;
  }

  .disabled {
    color: #ccc;
  }
}

.ad-container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 150px;
}

.text-ad-container {
  flex-direction: column;
}

.shortcut-wrapper {
  height: 90px;
}

.shortcut-box {
  text-align: center;
  justify-content: center;
  width: 650px;
  height: 90px;
  bottom: 150px;
  //background-color: #e7543d;
  //border-radius: 45px;
  //
  //text {
  //  font-size: 36px;
  //  color: white;
  //}
}
</style>
