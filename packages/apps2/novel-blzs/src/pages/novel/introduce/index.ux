<import
  name="table-content"
  src="../../../components/table-content.ux"
></import>
<import name="my-navbar" src="../../../components/navbar.ux"></import>
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import> -->

<template>
  <div id="drawer" class="page">
    <!-- <intad
      id="intad"
      if="showIntAd"
      show-one="{{true}}"
      event-name="ad_essay_insert"
    ></intad> -->
    <my-navbar show="{{!showDraw}}" show-status-bar="true"></my-navbar>
    <list class="wrapper">
      <list-item type="list-header" class="header">
        <image class="cover" src="{{info.cover}}"></image>
        <text class="title">{{ info.name }}</text>
        <text class="author">{{ info.author }}</text>
      </list-item>

      <list-item type="list-introduction" class="introduction">
        <text class="title">简介</text>
        <text class="content">
          {{ info.introduction }}
        </text>
      </list-item>

      <list-item
        type="list-contents"
        class="table-contents"
        @click="openDrawer"
      >
        <text class="title">目录</text>
        <text class="content">已完结 共{{ info.chapterCount }}章</text>
        <text class="icon">></text>
      </list-item>

      <list-item type="list-ad" class="ad-box">
        <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
      </list-item>

      <list-item class="btn-box" type="list-item">
        <input
          @click="toDetail"
          class="read-btn"
          type="button"
          value="开始阅读"
        />
      </list-item>
    </list>
    <apex-popup id="popup" position="left">
      <table-content
        id="table-content"
        @click-item="handleClickItem"
      ></table-content>
    </apex-popup>
  </div>
</template>

<script>
import { getBookInfoData } from '@/api/bookInfo'
import router from '@system.router'
import { getLastTimeNNovelChapter } from '@/helper/business'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 11111,
      introduction: '「都市+爽文+热血」',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
    },
    showDraw: false,
    showIntAd: false,
  },

  protected: {
    id: 2, // 小说id
    bookId: null, // 小说id
    chapterId: null, // 章节id
  },

  onInit() {
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
      }
    })
    if (
      this.bookId &&
      this.chapterId &&
      this.bookId !== 'null' &&
      this.chapterId !== 'null'
    ) {
      router.push({
        uri: 'pages/novel/reader',
        params: {
          bookId: Number(this.bookId),
          chapterId: Number(this.chapterId),
          ___PARAM_PAGE_ANIMATION___: {
            openEnter: `none`,
            closeEnter: `slide`,
            openExit: `slide`,
            closeExit: `slide`,
          },
        },
      })
    }
  },

  onShow() {
    const nativeAd = this.$child('nativead')
    if (nativeAd && nativeAd.onShow) {
      nativeAd.onShow()
    }
    this.showIntAd = true
  },

  onBackPress() {
    return false
  },

  openDrawer() {
    this.$child('popup').show()
    this.$child('table-content').initData(this.id)
    this.showDraw = true
  },

  closeDrawer() {
    this.$child('popup').hide()
    this.showDraw = false
  },

  handleClickItem({ detail }) {
    this.closeDrawer()

    router.push({
      uri: 'pages/novel/reader',
      params: {
        bookId: detail.data.bookId,
        chapterId: detail.data.id,
        name: this.info.name,
      },
    })
  },

  toDetail() {
    const toPage = params => {
      router.push({
        uri: 'pages/novel/reader',
        params,
      })
    }

    const params = {
      bookId: this.info.id,
      chapterId: this.info.firstChapterId,
      name: this.info.name,
    }

    getLastTimeNNovelChapter()
      .then(data => {
        if (data && data[this.info.id]) {
          const info = data[this.info.id]
          params.chapterId = info.chapterId
        }

        toPage(params)
      })
      .catch(() => {
        toPage(params)
      })
  },
})
</script>

<style lang="less">
.page {
  flex-direction: column;
  background-color: #f6f1e7;
}

.wrapper {
  flex-direction: column;
  padding: 190px 60px 0;
  height: 100%;
}

.header {
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .cover {
    width: 140px;
    height: 200px;
    border-radius: 8px;
  }

  .title {
    font-size: 40px;
    font-weight: bolder;
    color: #333333;
    margin: 20px 0 10px 0;
  }

  .author {
    font-size: 28px;
    color: #292938;
  }
}

.introduction {
  flex-direction: column;

  .title {
    font-size: 36px;
    font-weight: 600;
    color: #333333;
  }

  .content {
    margin-top: 20px;
    font-size: 30px;
    font-weight: 400;
    color: rgba(51, 51, 51, 0.7);
    line-height: 54px;
  }
}

.table-contents {
  align-items: center;
  border-width: 2px 0;
  border-color: rgba(56, 39, 12, 0.03);
  border-style: solid;
  padding: 32px 0;
  margin-top: 40px;

  .title {
    width: 64px;
    height: 32px;
    font-size: 32px;
    font-weight: bold;
    color: #38270c;
    line-height: 32px;
  }
  .content {
    width: 228px;
    height: 28px;
    font-size: 28px;
    color: #333333;
    margin-left: 20px;
  }
  .icon {
    font-size: 28px;
    margin-left: auto;
  }
}

.ad-box {
  width: 630px;
  background-color: #e8e0d1;
  border-radius: 20px;
  margin-top: 40px;
  justify-content: center;
}

.btn-box {
  padding-bottom: 80px;
  margin-top: 40px;

  .read-btn {
    width: 630px;
    height: 104px;
    background-color: rgba(66, 149, 243, 1);
    border-radius: 16px;
    font-size: 36px;
    font-weight: bolder;
    color: #ffffff;
  }
}
</style>
