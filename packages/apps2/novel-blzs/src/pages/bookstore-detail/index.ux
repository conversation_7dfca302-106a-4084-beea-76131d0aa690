<import name="my-spin" src="@quickapp/apex-ui/components/spin/index"></import>
<import name="short-cut" src="@quickapp/mc-ui/components/short-cut.ux"></import>

<template>
  <div class="wrapper">
    <refresh
      class="refresh"
      offset="132px"
      refreshing="{{pulldownrefreshing}}"
      @refresh="handlePulldownrefresh"
    >
      <my-spin tip="加载中..." nested="{{true}}" loading="{{firstLoading}}">
        <list slot="nested" class="list" @scrollbottom="handlePulluprefresh">
          <list-item
            class="item"
            type="listItem"
            for="{{dataList}}"
            tid="$idx"
            @click="toRead($item)"
          >
            <image class="img" src="{{$item.cover}}"></image>
            <div class="info">
              <text class="title">
                {{ $item.name }}
              </text>
              <text class="desc">{{ $item.introduction }}</text>
              <div>
                <text class="type">{{ title }}</text>
                <text class="chapter">共{{ $item.chapterCount }}章</text>
              </div>
            </div>
          </list-item>
          <!--<list-item class="loading-box" type="loading">-->
          <!--  <text if="{{pulluprefreshing}}">正在加载...</text>-->
          <!--  <text elif="{{!loadMoreEnabled}}">&#45;&#45;&#45;&#45;&#45;&#45; 我是有底线的 &#45;&#45;&#45;&#45;&#45;&#45;</text>-->
          <!--</list-item>-->
          <!-- 占位符 -->
          <!--<list-item-->
          <!--  if="{{showShortcutBtn}}"-->
          <!--  type="footer"-->
          <!--  style="height: 156px"-->
          <!--&gt;</list-item>-->
        </list>
      </my-spin>
    </refresh>
    <!--<input-->
    <!--  if="{{showShortcutBtn}}"-->
    <!--  type="button"-->
    <!--  class="add-desk"-->
    <!--  value="无需下载，添加至桌面"-->
    <!--  @click="addDesk"-->
    <!--&gt;</input>-->
    <div if="{{showShortcutBtn}}" type="footer" style="height: 156px"></div>
    <div class="add-desk">
      <!--<short-cut></short-cut>-->
    </div>
  </div>
</template>

<script>
import shortcut from '@system.shortcut'
import router from '@system.router'
import { getCategoryData } from '@/api/bookstore'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    pulluprefreshing: false,
    pulluprefresh: false,
    pulldownrefreshing: false,
    dataList: new Array(0).fill({
      name: '标题',
      introduction: '介绍',
      cover: '/assets/images/1.webp',
      type: '玄幻修仙',
      chapterCount: '1000章',
    }),
    // 总页数
    currentTotalPages: Number.MAX_SAFE_INTEGER,
    // 当前页
    currentPage: 1,
    pageSize: 10,
    showShortcutBtn: true,
    firstLoading: false,
  },

  protected: {
    title: '书城',
    id: 2,
  },

  onShow() {
    shortcut.hasInstalled({
      success: result => {
        this.showShortcutBtn = !result
      },
    })
  },

  computed: {
    loadMoreEnabled: {
      get() {
        return this.currentPage < this.currentTotalPages
      },
    },
  },

  onInit() {
    this.$page.setTitleBar({ text: this.title })
    this.firstLoading = true
    this.getData().finally(() => {
      this.firstLoading = false
    })
  },

  getData() {
    return getCategoryData({
      id: this.id,
      pageNum: this.currentPage,
      size: this.pageSize,
    }).then(res => {
      if (res) {
        const results = res.records

        if (this.currentPage > 1) {
          results.forEach(it => {
            const item = {
              ...it,
              introduction: it.introduction.substring(0, 16),
            }
            this.dataList.push(item)
          })
        } else {
          this.dataList = results.map(it => ({
            ...it,
            introduction: it.introduction.substring(0, 16),
          }))
        }
        this.currentTotalPages = res.pages
      }
    })
  },
  footerMove: function (params) {
    console.log(
      'footermove - scrollY:' +
        params.scrollY +
        ' percent:' +
        params.percent +
        ' isDrag:' +
        params.isDrag +
        ' refreshing:' +
        params.refreshing
    )
  },
  handlePulluprefresh() {
    console.log('上拉')
    if (this.loadMoreEnabled) {
      this.pulluprefreshing = true
      this.currentPage += 1
      this.getData().finally(() => {
        this.pulluprefreshing = false
      })
    } else {
      // this.$element('refresh').stopPullUpRefresh()
      this.pulluprefreshing = false
    }
  },
  handlePulldownrefresh() {
    console.log('下拉')
    this.pulldownrefreshing = true
    this.currentPage = 1
    this.getData().finally(() => {
      this.pulldownrefreshing = false
    })
  },

  addDesk() {
    shortcut.install({
      success: () => {
        console.log('成功。。。。')
        this.showShortcutBtn = false
      },
      fail: (data, code) => {
        console.log('失败。。。')
        this.showShortcutBtn = true
        console.log(`handling fail, code = ${code}, errorMsg=${data}`)
      },
    })
  },

  toDetail() {
    router.push({
      uri: 'pages/novel/introduce',
    })
  },
  toRead(item) {
    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },
})
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  height: 100%;
  .list {
    flex: 1;
    padding: 20px 0 20px 20px;
    background-color: #ffffff;
  }
}

.refresh {
  flex: 1;
}

.list {
  columns: 3;
}

.loading-box {
  height: 130px;
  width: 100%;
  justify-content: center;
  align-items: center;
  /* background-color: #faebd7; */
  .loading {
    color: rgba(0, 0, 0, 0.3);
    /* font-size: 22px; */
  }
}

.item {
  flex-direction: column;
  padding: 20px 9px;

  .img {
    width: 212px;
    height: 280px;
    border-radius: 16px;
  }

  .info {
    flex: 1;
    flex-direction: column;
    padding-top: 12px;

    .title {
      font-size: 32px;
      font-weight: bolder;
      color: #292938;
      line-height: 32px;
      lines: 1;
    }

    .desc {
      font-size: 28px;
      color: #292938;
      line-height: 42px;
      margin-top: 12px;
      margin-bottom: 24px;
      lines: 2;
    }

    .type {
      margin-right: 20px;
      /* color: #ff8b20; */
      font-weight: bold;
    }

    .chapter {
      color: rgba(41, 41, 56, 0.4);
    }

    .chapter,
    .type {
      font-size: 24px;
      line-height: 24px;
      lines: 1;
    }
  }
}

.add-desk {
  position: fixed;
  bottom: 76px;
  left: 175px;
  width: 400px;
  height: 80px;
}
</style>
