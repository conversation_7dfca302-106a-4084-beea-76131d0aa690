{"id": "10095", "package": "com.zzts.listen.novel", "configCode": "cf_20221118152753", "name": "之之听书", "versionName": "1.0.14", "versionCode": 15, "privacyUrl": "https://app-h5.springtool.cn/zzts/agreement/privacy.html", "userUrl": "https://app-h5.springtool.cn/zzts/agreement/user.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/zzts/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/11167694/81d1", "minPlatformVersion": 1090, "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "service.texttoaudio"}, {"name": "system.audio"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "template/official": "demo-template", "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Home": {"component": "index"}, "pages/Web": {"component": "index"}, "pages/Service": {"component": "index"}, "pages/BookList": {"component": "index"}, "pages/Content/Introduce": {"component": "index"}, "pages/Content/Detail": {"component": "index"}, "pages/Content/Reader": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}}}, "display": {"themeMode": 0, "menuBarData": {"menuBar": false}, "menu": true, "pages": {"pages/Home": {"titleBarText": "", "titleBar": false, "menu": true, "menuBar": true, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fullScreen": false, "titleBarBackgroundColor": "#000D26", "menuBarData": {"menuBar": false}, "titleBarTextColor": "#fff"}, "pages/BookList": {"menu": true, "menuBar": true, "titleBarText": "都市", "titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141"}, "pages/Content/Detail": {"titleBarText": "", "menu": true, "menuBar": true, "titleBarBackgroundColor": "#ffffff", "titleBarTextColor": "#414141"}, "pages/Content/Introduce": {"titleBarText": "", "menu": true, "menuBar": true, "titleBarBackgroundColor": "#f8f8f8", "titleBarTextColor": "#414141"}, "pages/Web": {"menu": true, "menuBar": true, "titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141"}, "pages/Content/Reader": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": true}, "pages/Flash": {"titleBarText": "", "titleBar": false, "menu": true, "menuBar": true, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "pages/Service": {"menu": true, "menuBar": true, "titleBarText": "在线客服"}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}}}}