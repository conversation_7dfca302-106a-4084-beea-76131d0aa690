import shortcut from '@system.shortcut'

export default {
  namespaced: true,

  state: {
    // 是否已经添加到桌面图标
    hasShortcut: false,
  },

  getters: {},

  mutations: {
    setShortcutState(state, isExist) {
      state.hasShortcut = isExist
    },
  },

  actions: {
    addDesk({ commit }) {
      const shortcut = require('@system.shortcut')
      shortcut.install({
        success: () => {
          commit('setShortcutState', true)
        },
        fail: () => {
          commit('setShortcutState', false)
        },
      })
    },
    shortcutHasInstalled({ commit }) {
      const shortcut = require('@system.shortcut')
      return new Promise(resolve => {
        shortcut.hasInstalled({
          success: result => {
            // commit('setShortcutState', result)
            result(result)
          },
        })
      })
    },
  },
}
