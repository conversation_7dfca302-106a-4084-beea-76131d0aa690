/**
 * 书架
 */

import { request } from '@quickapp/business/lib/config'

export function getBookshelfData() {
  return request({
    method: 'GET',
    url: '/novel/bookshelf',
  })
}

/**
 * 移除数据
 * @param {number[]} idList id列表
 * @returns {Promise<any>}
 */
export function removeBookShelf({ idList }) {
  return request({
    method: 'POST',
    url: '/novel/bookshelf/remove',
    data: { idList },
  })
}
