<import name="bookstore-list" src="./bookstore-list.ux"></import>

<template>
  <div class="wrapper">
    <div class="top">
      <text class="text">红霞阅读</text>
    </div>
    <swiper
      if="{{hotList && hotList.length}}"
      class="swiper"
      autoplay="true"
      index="{{swiperOpt.index}}"
      interval="{{swiperOpt.interval}}"
      indicator="{{swiperOpt.indicator}}"
      indicator-bottom="-40"
      indicator-color="#D8D8D8"
      indicator-selected-color="#09D24E"
      nextmargin="250" 
      previousmargin="250" 
    >
        <div class="item-wrapper" for="item in hotList"  @click="toRead(item)">
          <stack>
            <div class="item-bg">
              <div class="r">
                <text class="text">{{ item.name }}</text>
                <text class="author">{{ item.author }}</text>
                <!-- <text class="intro">{{ item.introduction }}</text> -->
              </div>
            </div>
            <image class="img" src="{{item.cover}}"></image>
          </stack>
        </div>
    </swiper>
    <bookstore-list
      @see-more="handleSeeMoreEvt"
      category-list="{{categoryList}}"
    ></bookstore-list>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookstoreData } from '@/api/bookstore'

export default {
  data: {
    hotList: [],
    categoryList: [],
    swiperOpt: {
      index: 0,
      interval: 3000,
      indicator: true,
    },
  },

  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    showSeeMore: {
      type: Boolean,
      default: true,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },

  handleLifeCycleShow() {
    this.handleChangeIndex()
  },

  handleSeeMoreEvt({ detail }) {
    router.push({
      uri: 'pages/bookstore-detail',
      params: {
        title: detail.category,
        id: detail.id,
      },
    })
  },

  toRead(item) {
    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },

  getData() {
    return getBookstoreData().then(res => {
      if (res) {
        if (res.hotRecommend) {
          this.hotList = res.hotRecommend
        }
        if (res.categoryList) {
          this.categoryList = res.categoryList
        }
      }
    })
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
}

.top {
  width: 100%;
  /* height: 88px; */
  padding: 40px 30px;
  justify-content: flex-start;

  .text {
    font-weight: bolder;
    font-size: 44px;
    color: #333333;
    line-height: 36px;
  }
}

.swiper {
  height: 620px;
  stack {
    height: 100%;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
  }
  .item-wrapper {
    align-items: flex-end;
    stack {
      height: 100%;
    }
    .img {
      top: 140px;
      /* left: 40px; */
      width: 284;
      height: 426;
      border-radius: 10px;
    }
    .item-bg {
      /* height: 272px; */
      top: 0;
      /* background-color: rgba(34, 214, 77, .3); */
      border-radius: 20px;
      margin-right: 10px;
      margin-left: 10px;
      .r {
        flex-direction: column;
        /* margin-left: 30px; */
        /* justify-content: space-around; */
        /* margin-left: 180px; */
        .text {
          /* font-weight: bold; */
          color: #333;
          line-height: 36px;
          lines: 1;
          margin-top: 36px;
          text-overflow: ellipsis;
          margin-top: 30px;
          font-size: 40px;
          text-align: center;
        }

        .author {
          font-size: 24px;
          color: #292939;
          /* opacity: 0.4; */
          margin-bottom: 30px;
          margin-top: 15px;
          /* margin-top: 14px; */
          text-align: center;
        }
        .intro {
          lines: 2;
          color: #fff;
          font-size: 24px;
          opacity: 0.6;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>
