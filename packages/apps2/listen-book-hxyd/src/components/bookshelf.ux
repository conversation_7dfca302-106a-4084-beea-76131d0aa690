<import name="bookshelf-list" src="./bookshelf-list.ux"></import>

<template>
  <div class="wrapper">
    <div class="header">
      <text class="text">书架</text>
    </div>
    <div if="{{swiperList && swiperList.length}}" class="swiper-box">
      <swiper
        class="swiper"
        autoplay="true"
        index="{{swiperOpt.index}}"
        interval="{{swiperOpt.interval}}"
        indicator="{{swiperOpt.indicator}}"
      >
        <block for="value in swiperList">
          <div if='{{value.dataType === "ad"}}'>
            <text>广告容器</text>
          </div>
          <stack class="item__wrapper" else @click="toRead(value)">
            <!-- <div class="item__wrapper__desc">
              <text class="item__wrapper__desc__text">
                {{ value.introduction }}
              </text>
            </div> -->
            <div class="item__wrapper__top">
              <div class="item__wrapper__top__info">
                <text class="item__wrapper__top__info__title">
                  {{ value.name }}
                </text>
                <text class="item__wrapper__top__info__auth">
                  {{ value.author }}
                </text>
                <text class="item__wrapper__top__info__intro">
                  {{ value.introduction }}
                </text>
              </div>
              <image
                class="item__wrapper__top__cover"
                src="{{value.cover}}"
              ></image>
            </div>
            <image class="bz" src="/assets/images/bianzu.png"></image>
          </stack>
        </block>
      </swiper>
    </div>
    <div class="bookshelf-box">
      <bookshelf-list
        @delete-success="handleDeleteSuccess"
        novel-list="{{novelList}}"
      ></bookshelf-list>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
import { getBookshelfData } from '@/api/bookshelf'

export default {
  data: {
    title: 'Hello World. 书架.',
    swiperOpt: {
      index: 0,
      interval: 3333000,
      indicator: false,
    },
    swiperList: [],
    novelList: [],
  },

  props: {
    // index: 组件索引
    index: {
      type: Number,
    },
    currentIndex: {
      type: Number,
    },
    // 修改 lifeCycleShow 的值，执行 handleLifeCycleShow
    lifeCycleShow: {
      type: Number,
    },
  },

  onInit() {
    this.$watch('currentIndex', 'handleChangeIndex')
    this.$watch('lifeCycleShow', 'handleLifeCycleShow')
  },

  handleChangeIndex() {
    if (this.index === this.currentIndex) {
      // 执行逻辑
      this.getData()
    }
  },

  handleLifeCycleShow() {
    this.handleChangeIndex()
  },

  getData() {
    return getBookshelfData().then(res => {
      if (res) {
        if (res.recommendList) {
          // 插入广告
          // this.swiperList = res.recommendList
          //   .map(it => [{ ...it, dataType: 'novel' }, { dataType: 'ad' }])
          //   .flat()
          this.swiperList = res.recommendList
        }
        if (res.myBookshelfRecommend) {
          this.novelList = res.myBookshelfRecommend.map(it => ({
            ...it,
            isChecked: false,
          }))
        }
        console.log(
          'this.swiperList',
          JSON.stringify(Object.keys(this.swiperList[0]))
        )
      }
    })
  },

  toRead(item) {
    console.log('read....')
    router.push({
      uri: 'pages/novel/introduce',
      params: {
        id: item.id,
      },
    })
  },

  handleDeleteSuccess() {
    this.$emit('deleteSuccess')
    this.getData()
  },
}
</script>

<style lang="less">
.wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
}

.header {
  justify-content: center;
  width: 100%;
  height: 88px;
  padding: 0 30px;

  .text {
    font-size: 36px;
    text-align: center;
    font-weight: bolder;
    color: #333333;
  }
}

.swiper-box {
  border-radius: 8px;
  .swiper {
    width: 100%;
    height: 500px;
    border-radius: 8px;
  }
  .item__wrapper {
    border-radius: 8px;
    .bz {
      width: 122px;
      height: 122px;
      right: 40px;
      bottom: 0px;
      position: absolute;
    }
    &__top {
      border-radius: 24px;
      position: relative;
      &__cover {
        border-radius: 10px;
        position: absolute;
        width: 250px;
        height: 374px;
        box-shadow: inset -4px -4px 2px 0px rgba(0,0,0,0.16);
        top: 20px;
        left: 20px;
      }

      &__info {
        margin-left: 60px;
        padding-right: 30px;
        flex-direction: column;
        /* justify-content: space-around; */
        align-items: flex-end;
        background-color: #FFF;
        width: 628px;
        height: 420px;
        border-radius: 8px;

        &__title {
          width: 350px;
          font-size: 32px;
          font-weight: bolder;
          color: #292938;
          text-align: right;
          margin-top: 50px;
        }

        &__auth {
          width: 350px;
          font-size: 28px;
          color: #292938;
          margin-top: 12px;
          text-align: right;
          margin-top: 20px;
        }
        &__intro {
          width: 350px;
          color: #BABAC0;
          font-size: 24px;
          lines: 4;
          text-align: right;
          text-overflow: ellipsis;
          margin-top: 35px;
          line-height: 42px;
        }
      }
    }

    &__desc {
      height: 360px;
      align-items: flex-end;
      padding: 40px;
      background-color: #e6ebfb;
      border-radius: 40px;
      margin-top: 72px;

      &__text {
        lines: 3;
        font-size: 28px;
        line-height: 45px;
        text-overflow: ellipsis;
      }
    }
  }
}

.bookshelf-box {
  margin-top: 0px;
}
</style>
