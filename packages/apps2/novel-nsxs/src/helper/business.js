import utils from './utils'
import router from '@system.router'

// 已经阅读的小说
const HAS_READ_NOVEL_KEY = 'HAS_READ_NOVEL_KEY'
// 已经阅读的小说章节
const HAS_READ_NOVEL_CHAPTER_KEY = 'HAS_READ_NOVEL_CHAPTER_KEY'
// 上次阅读的小说章节
const NOVEL_CHAPTER_KYE = 'NOVEL_CHAPTER_KYE'
// 小说字体大小 key
const STORE_NOVEL_FONT_SIZE_KEY = 'STORE_NOVEL_FONT_SIZE_KEY'
// 主题
const STORE_THEME = 'STORE_THEME'
// 默认字体列表
export const fontSizeList = [32, 36, 44, 52]
// 默认字体大小
export const defaultFontSize = fontSizeList[1]
// 主题列表
export const themeList = [
  {
    bgColor: '#FFFFFF',
    frontColor: '#38270C',
    settingPanel: {
      bgColor: '#E8E0D1',
      frontColor: '#333333',
      borderColor: '#A19378',
      disabledColor: '#ccc',
    },
    theme: 'light',
  },
  {
    bgColor: '#F6F1E7',
    frontColor: '#38270C',
    settingPanel: {
      bgColor: '#E8E0D1',
      frontColor: '#333333',
      borderColor: '#A19378',
      disabledColor: '#ccc',
    },
    theme: 'light',
  },
  {
    bgColor: '#353638',
    frontColor: 'rgba(255,255,255,0.8)',
    settingPanel: {
      bgColor: '#0D121B',
      frontColor: '#FFFFFF',
      borderColor: '#ED5B3B',
      disabledColor: '#666',
    },
    theme: 'dark',
  },
]
// 默认主题
export const defaultTheme = themeList[1]

/**
 * 获取小说字体大小
 *
 * @returns {Promise<number>}
 */
export function getNovelFontSize() {
  return new Promise(resolve => {
    utils.storage
      .get(STORE_NOVEL_FONT_SIZE_KEY)
      .then(fontSize => {
        if (fontSize) {
          resolve(fontSize)
        } else {
          resolve(defaultFontSize)
        }
      })
      .catch(() => {
        resolve(defaultFontSize)
      })
  })
}

/**
 * 设置小说字体大小
 *
 * @param {number} fontSize 字体大小
 * @returns {Promise<number>}
 */
export function setNovelFontSize(fontSize) {
  return utils.storage.set(STORE_NOVEL_FONT_SIZE_KEY, fontSize)
}

/**
 * 获取小说主题
 *
 * @returns {Promise<number>}
 */
export function getNovelTheme() {
  return new Promise(resolve => {
    utils.storage
      .get(STORE_THEME)
      .then(theme => {
        if (theme) {
          resolve(theme)
        } else {
          resolve(defaultTheme)
        }
      })
      .catch(() => {
        resolve(defaultTheme)
      })
  })
}

/**
 * 设置小说背景
 *
 * @param {number} theme 主题
 * @returns {Promise<number>}
 */
export function setNovelTheme(theme) {
  return utils.storage.set(STORE_THEME, theme)
}

/**
 * 获取小说上次阅读的章节
 * @returns {Promise | Promise<unknown>}
 */
export function getLastTimeNNovelChapter() {
  return utils.storage.get(NOVEL_CHAPTER_KYE)
}

/**
 * 缓存小说上次阅读的章节
 * @param bookId {number} 小说id
 * @param chapterId {number} 章节id
 * @param chapterName {string} 章节标题
 * @returns {Promise | Promise<{[k] in string: {chapterId: number, chapterName: string}}>}
 */
export function setLastTimeNovelChapter({ bookId, chapterId, chapterName }) {
  return getLastTimeNNovelChapter().then(data => {
    const newData = {
      [bookId]: {
        chapterId,
        chapterName,
      },
    }

    const resData = {
      ...data,
      ...newData,
    }

    return utils.storage.set(NOVEL_CHAPTER_KYE, data ? resData : newData)
  })
}

/**
 * 获取缓存已经阅读过的小说章节
 * @returns {Promise<unknown>}
 */
export function getHasReadNovelChapter() {
  return utils.storage.get(HAS_READ_NOVEL_CHAPTER_KEY)
}

/**
 * 缓存已经阅读过的小说章节
 * @param bookId 小说id
 * @param chapterId 章节id
 * @param chapterName 章节标题
 * @returns {Promise | Promise<{[k] in string: Array<{chapterId: number, chapterName: string}>}>}
 */
export function setHasReadNovelChapter({ bookId, chapterId, chapterName }) {
  return getHasReadNovelChapter().then(data => {
    const setFirstData = () =>
      utils.storage.set(HAS_READ_NOVEL_CHAPTER_KEY, {
        [bookId]: [{ chapterId, chapterName }],
      })

    if (data) {
      const bookData = data[bookId]

      if (bookData) {
        if (!bookData.find(it => it.chapterId === chapterId)) {
          bookData.push({ chapterId, chapterName })
        }
        return utils.storage.set(HAS_READ_NOVEL_CHAPTER_KEY, {
          ...data,
          [bookId]: bookData,
        })
      }

      return utils.storage.set(HAS_READ_NOVEL_CHAPTER_KEY, {
        ...data,
        [bookId]: [{ chapterId, chapterName }],
      })
    }

    return setFirstData()
  })
}

/**
 * 获取已经阅读过的小说
 * @returns {Promise | Promise<unknown>}
 */
export function getHasReadNovel() {
  return utils.storage.get(HAS_READ_NOVEL_KEY)
}

/**
 * 缓存已经阅读过的小说
 */
export function setHasReadNovel({
  bookId,
  chapterId,
  chapterName,
  content,
  nextChapterId,
  preChapterId,
  name,
}) {
  getHasReadNovel().then(list => {
    if (list) {
      if (!list.find(it => it.bookId === bookId)) {
        list.push({
          bookId,
          chapterId,
          chapterName,
          content,
          nextChapterId,
          preChapterId,
          name,
        })
      }
      utils.storage.set(HAS_READ_NOVEL_KEY, list)
    } else {
      utils.storage.set(HAS_READ_NOVEL_KEY, [
        {
          bookId,
          chapterId,
          chapterName,
          content,
          nextChapterId,
          preChapterId,
          name,
        },
      ])
    }
  })
}

export function toPrivacyPage() {
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: __MANIFEST__.privacyUrl,
    },
  })
}

export function toUserPage() {
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: __MANIFEST__.userUrl,
    },
  })
}

export function toGuidelinesForTortClaims() {
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: __MANIFEST__.guidelinesForTortClaimsUrl,
    },
  })
}

export function toQuestionUrl() {
  router.push({
    uri: 'pages/Web',
    params: {
      webSrc: __MANIFEST__.questionUrl,
    },
  })
}
