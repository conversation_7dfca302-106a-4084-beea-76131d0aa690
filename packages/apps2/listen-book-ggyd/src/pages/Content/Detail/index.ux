<import name="apex-icon" src="@quickapp/apex-ui/components/icon/index"></import>
<import
  name="apex-popup"
  src="@quickapp/apex-ui/components/popup/index"
></import>
<import
  name="table-content"
  src="../../../components/table-content.ux"
></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import> -->
<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<!-- <import
  name="nativebanner"
  src="@quickapp/business/lib/nativebanner.ux"
></import> -->

<template>
  <div class="wrapper">
    <!-- <intad
      if="showIntAd"
      @close="onIntAdClose"
      event-name="ad_essay_insert"
    ></intad> -->
    <image class="cover" src="{{chapter.cover}}"></image>
    <text class="novel-name">{{ name }}</text>
    <text class="author">{{ chapter.author }}</text>
    <div class="text-ad-container">
      <!-- <nativead
        id="nativead"
        event-name="ad_essay"
        show-ad-free="{{true}}"
      ></nativead> -->
    </div>
    <div class="chapter">
      <text class="chapter__name">
        {{ chapter.chapterName }}
      </text>
      <apex-icon type="options" size="48" @click="openDrawer"></apex-icon>
    </div>
    <div class="control">
      <image
        class="control__icon"
        src="/assets/images/ic_player_prev.png"
        @click="prevChapter"
      ></image>
      <image
        if="{{isPlay}}"
        @click="audioPause"
        class="control__icon"
        src="/assets/images/ic_player_pause.png"
      ></image>
      <image
        else
        @click="audioPlay"
        class="control__icon"
        src="/assets/images/ic_player_play.png"
      ></image>
      <image
        class="control__icon"
        src="/assets/images/ic_player_next.png"
        @click="nextChapter"
      ></image>
    </div>
    <apex-popup id="popup" position="left">
      <table-content
        id="table-content"
        @click-item="handleClickItem"
      ></table-content>
    </apex-popup>
    <!--<text style="color: white">{{ chapter.content }}</text>-->
    <div if="{{false}}" class="ad-container">
      <!-- <nativebanner id="banner" event-name="ad_essay_bottom"></nativebanner> -->
    </div>
  </div>
</template>

<script>
import { getChapterContentData } from '@/api/bookInfo'
import texttoaudio from '@service.texttoaudio'
import { showToast, storage } from '@quickapp/utils'
import audio from '@system.audio'
import adsdk from '@quickapp/business/lib/adsdk'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

const pageConfig = {
  private: {
    chapter: {},
    audioFileList: [],
    contentList: [], // 把小说内容分段
    maxLen: 100, // 每段语音段长度
    currentAudioIndex: 0, // 当前语音段索引
    isPlay: false,
    isCanSwitch: false, // 切换章节，加个标识延时一下
    canSwitchTimer: null,
    prevTime: Date.now(),
    showIntAd: false,
    showIntCount: 0,
  },

  protected: {
    bookId: null,
    chapterId: null,
    name: '开局买大力',
  },

  onInit() {
    this.getData()

    audio.onplay = () => {
      this.isPlay = true
    }

    audio.onpause = () => {
      this.isPlay = false
    }

    audio.onstop = () => {
      this.isPlay = false
    }

    audio.onended = () => {
      if (this.currentAudioIndex < this.audioFileList.length - 1) {
        this.currentAudioIndex++
        this.setAudioSrc()
        this.audioPlay()
      } else {
        showToast('当前章节已经播放完毕')
      }
    }

    texttoaudio.onttsstatechange = data => {
      this.isPlay = data.state !== 'onStop'
      console.log(`utteranceId: ${data.utteranceId}, state: ${data.state}`)
    }
  },

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    try {
      this.$child('banner').reportNativeShow()
    } catch (e) {}
  },

  onDestroy() {
    this.stopAll()
  },

  onHide() {
    this.stopAll()
  },

  stopAll() {
    this.audioStop()

    this.texttoaudioStop()
  },

  getData(chapterId = this.chapterId, bookId = this.bookId) {
    this.audioStop()
    this.texttoaudioStop()
    this.currentAudioIndex = 0
    getChapterContentData({ bookId, chapterId })
      .then(res => {
        if (res) {
          this.chapter = res
          this.speakAudio(res.content)

          // this.handleRes()
        }
      })
      .finally(() => {
        this.isCanSwitch = false

        this.canSwitchTimer = setTimeout(() => {
          this.isCanSwitch = true
        }, 2000)
      })
  },

  speakAudio(content = this.chapter.content) {
    texttoaudio.speak({
      lang: 'zh_CN',
      content,
      pitch: 1,
      rate: 1,
      success: function (data) {
        console.log(`handling success, utteranceId = ${data.utteranceId}`)
      },
      fail: function (data, code) {
        console.log(`handling fail, code = ${code}`)
      },
    })
  },

  handleRes() {
    this.splitContent()
    this.batchToAudio()
  },

  nextChapter() {
    if (this.isCanSwitch) {
      if (this.chapter.nextChapterId) {
        this.getData(this.chapter.nextChapterId)
        if (this.showIntCount % 2 === 0) {
          this.showIntAd = true
        }
        this.showIntCount++
      } else {
        showToast('看完了')
      }
    } else {
      showToast('加载中,请稍后')
    }
  },

  prevChapter() {
    if (this.isCanSwitch) {
      if (this.chapter.preChapterId) {
        this.getData(this.chapter.preChapterId)
      } else {
        showToast('没有上一章了')
      }
    } else {
      showToast('加载中,请稍后')
    }
  },

  onIntAdClose() {
    this.showIntAd = false
  },

  splitContent(content = this.chapter.content) {
    const len = Math.ceil(content.length / this.maxLen)
    let start = 0
    let end = this.maxLen
    this.contentList = []
    for (let i = 0; i < len; i++) {
      this.contentList.push(content.slice(start, end))
      start = this.maxLen * i
      end = this.maxLen + start
    }
  },

  batchToAudio() {
    this.getLocalAudio()
      .then(localAudio => {
        console.log('this.chapter.chapterId', this.chapter.chapterId)
        if (
          localAudio &&
          localAudio[this.bookId] &&
          localAudio[this.bookId][this.chapter.chapterId]
        ) {
          this.audioFileList = localAudio[this.bookId][this.chapter.chapterId]
          console.log('this.audioFileList', this.audioFileList)
          this.setAudioSrc()
        } else {
          Promise.all(this.contentList.map(it => this.textToAudioFile(it)))
            .then(values => {
              this.setLocalAudio(this.chapter.chapterId, values)
              this.audioFileList = values
              this.setAudioSrc()
            })
            .catch(e => {})
        }
      })
      .catch(e => {})
  },

  setAudioSrc() {
    audio.src = this.audioFileList[this.currentAudioIndex].filePath
    setTimeout(() => {
      this.audioPlay()
    }, 1500)
  },

  audioPlay() {
    // if (!audio.src) {
    //   showToast('正在加载...')
    // } else {
    //   audio.play()
    // }

    this.speakAudio()
  },

  audioPause() {
    // audio.pause()
    this.texttoaudioStop()
  },

  texttoaudioStop() {
    texttoaudio.stop()
  },

  audioStop() {
    audio.stop()
  },

  openDrawer() {
    this.$child('popup').show()
    this.$child('table-content').initData(this.bookId)
    this.showDraw = true
  },

  closeDrawer() {
    this.$child('popup').hide()
    this.showDraw = false
  },

  async setLocalAudio(chapterId, contentList) {
    let localAudio = null
    try {
      localAudio = await this.getLocalAudio()
    } catch (e) {}

    let currentBookAudio = null

    if (localAudio) {
      currentBookAudio = localAudio[this.bookId]
      // 当前书是否已经转了文件了
      if (currentBookAudio) {
        // 当前小说的章节是否已经转了文件了
        if (currentBookAudio[chapterId]) {
          if (!localAudio[this.bookId][chapterId]) {
            localAudio[this.bookId][chapterId] = contentList
          }
        } else {
          localAudio[this.bookId][chapterId] = contentList
        }
      }
      // 没有的话直接设置
      else {
        localAudio[this.bookId] = {
          [chapterId]: contentList,
        }
      }
    } else {
      localAudio = {
        [this.bookId]: {
          [chapterId]: contentList,
        },
      }
    }

    return storage.set('novel_audio', localAudio)
  },

  async getLocalAudio() {
    return storage.get('novel_audio')
  },

  textToAudioFile(content) {
    return new Promise((resolve, reject) => {
      texttoaudio.textToAudioFile({
        lang: 'zh_CN',
        content,
        pitch: 1,
        rate: 1,
        success: data => {
          console.log(
            `handling success,filePath = ${data.filePath}, utteranceId = ${data.utteranceId}`
          )
          resolve(data)
        },
        fail: (data, code) => {
          showToast(data + code)
          reject({ data, code })
        },
      })
    })
  },

  handleClickItem({ detail }) {
    this.closeDrawer()

    this.getData(detail.data.id)
    // router.push({
    //   uri: 'pages/Content/Detail',
    //   params: {
    //     bookId: detail.data.bookId,
    //     chapterId: detail.data.id,
    //     name: this.name,
    //   },
    // })
  },
}

// const m_global = global.__proto__ || global
//
// if (m_global.is_from_playable) {
//   pageConfig.onMenuPress = function () {}
// }

export default setPageMenuConfig(pageConfig)
</script>

<style lang="less">
@import '../../../assets/styles/style';
.wrapper {
  flex-direction: column;
  align-items: center;
  background-color: @mainBgColor;
  padding-top: 102px;
}

.cover {
  width: 550px;
  border-radius: 20px;
}

.novel-name {
  font-size: 40px;
  font-weight: bolder;
  //color: #ffffff;
  line-height: 56px;
  margin-top: 40px;
}

.author {
  font-size: 28px;
  color: rgba(0, 0, 0, 0.7);
  line-height: 40px;
  margin-top: 26px;
}

.chapter {
  width: 710px;
  height: 84px;
  padding: 0 70px;
  margin-top: 72px;
  justify-content: space-between;
  align-items: center;
  background-image: url('/assets/images/chapter_bg.png');
  background-size: cover;

  &__name {
    font-size: 28px;
    //color: #ffffff;
  }
}

.control {
  width: 610px;
  height: 120px;
  margin-top: 26px;
  align-items: center;
  justify-content: space-between;
  background-color: @mainLightColor;
  border-radius: 16px;
  padding: 0 144px;
  margin-bottom: 180px;

  &__icon {
    width: 60px;
    height: 60px;
  }
}

.ad-container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 150px;
}

.text-ad-container {
  margin-top: 72px;
  width: 80%;
  flex-direction: column;
}
</style>
