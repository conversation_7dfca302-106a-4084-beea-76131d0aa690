<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import name="apex-popup" src="@quickapp/apex-ui/components/popup/index"></import>
<import name="chapter-list" src="../../../components/chapter-list"></import>

<template>
  <div id="drawer" class="page">
    <div class="top">
      <image class="cover" src="{{info.cover}}"></image>
      <div class="title-box">
        <text class="title">{{ info.name }}</text>
        <text class="category">{{ info.category }}</text>
        <input
          show="{{showAddBookShelf}}"
          @click="addBookShelf"
          class="add-bookshelf"
          type="button"
          value="加入书架"
        />
      </div>
    </div>

    <div class="introduction-box">
      <text class="introduction-box__title">简介</text>
      <text class="introduction">{{ info.introduction }}</text>
    </div>

    <div class="ad-box">
      <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
    </div>

    <div class="chapter-box">
      <chapter-list
        book-id="{{id}}"
        @click-item="handleClickItem"
      ></chapter-list>
    </div>
    <div class="footer">
      <!--<input-->
      <!--  show="{{showAddBookShelf}}"-->
      <!--  class="add-bookshelf"-->
      <!--  type="button"-->
      <!--  value="加入书架"-->
      <!--  @click="addBookShelf"-->
      <!--/>-->
      <input class="read" type="button" value="马上阅读" @click="toDetail" />
    </div>
  </div>
</template>

<script>
import { addBookShelf, getBookInfoData } from '@/api/comics'
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
      isInShelf: false,
      chapterId: null, // 读到到章节
    },
    showDraw: false,
    showAddBookShelf: true,
  },

  protected: {
    id: 1, // 小说id
  },

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
        this.showAddBookShelf = !res.isInShelf
      }
    })
  },

  toDetail() {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: this.id,
        chapterId: this.info.chapterId || this.info.firstChapterId,
      },
    })
  },

  handleClickItem({ detail }) {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.bookId,
        chapterId: detail.id,
      },
    })
  },

  addBookShelf() {
    addBookShelf([this.info.id])
      .then(() => {
        showToast('成功加入书架')
        this.showAddBookShelf = false
      })
      .catch(() => {
        showToast('失败加入书架')
      })
  },
})
</script>

<style lang="less">
@import '../../../assets/styles/style';

.page {
  flex-direction: column;
  padding: 14px 30px 188px;
  background-color: @bgColor;
  //background-image: url("/assets/images/introduce_bg.webp");
  //background-size: cover;
}

.top {
  flex-direction: row;
  margin-top: 32px;
}

.cover {
  width: 208px;
  height: 276px;
  border-radius: 16px;
}

.ad-box {
  border-radius: 20px;
  margin-top: 40px;
  justify-content: center;
}

.title-box {
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 32px;

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #ffffff;
  }

  .category {
    text-align: center;
    height: 44px;
    font-size: 28px;
    font-weight: bolder;
    color: rgba(255, 216, 25, 1);
    margin-top: 12px;
  }

  .add-bookshelf {
    width: 192px;
    height: 80px;
    background-color: rgba(163, 255, 62, 0.2);
    border-radius: 40px;
    font-size: 28px;
    color: rgba(163, 255, 62, 1);
    margin-top: auto;
  }
}

.introduction-box {
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.07);
  border-radius: 16px;
  margin-top: 48px;
  padding: 32px;

  &__title {
    font-size: 32px;
    font-weight: bolder;
    color: #ffffff;
    line-height: 44px;
  }
}

.introduction {
  font-size: 28px;
  margin-top: 24px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 48px;
}

.chapter-box {
  background-color: rgba(255, 255, 255, 0.07);
  padding: 0 32px;
  border-radius: 16px;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750px;
  height: 168px;
  padding-top: 32px;
  padding-right: 32px;
  padding-left: 32px;
  justify-content: flex-end;
  background-color: rgba(27, 31, 23, 1);

  input {
    text-align: center;
    height: 84px;
    font-size: 32px;
    color: white;
    border-radius: 12px;
  }

  .add-bookshelf {
    width: 218px;
    background: linear-gradient(270deg, #ff9c07 0%, #ff7010 100%);
    border-radius: 24px;
    padding: 0 24px;
    margin-right: 24px;
  }

  .read {
    flex: 1;
    height: 112px;
    background: linear-gradient(43deg, #3eff81 0%, #a3ff3e 100%);
    box-shadow: 0 4px 18px 0 rgba(255, 180, 0, 0.2);
    border-radius: 56px;
    color: #000;
    font-size: 32px;
  }
}
</style>
