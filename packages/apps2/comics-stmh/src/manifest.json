{"id": "10098", "package": "com.stxs.comic", "configCode": "cf_20221123153505", "name": "水獭漫画", "versionName": "1.0.13", "versionCode": 14, "privacyUrl": "https://app-h5.springtool.cn/stmh/agreement/privacy.html", "userUrl": "https://app-h5.springtool.cn/stmh/agreement/user.html", "guidelinesForTortClaimsUrl": "https://app-h5.springtool.cn/stmh/agreement/guidelines-for-tort-claims.html", "questionUrl": "https://wj.qq.com/s2/11216314/e3a0", "serviceUrl": "https://app-h5.springtool.cn/static/views/service/novel.html", "minPlatformVersion": 1080, "icon": "/assets/images/logo.png", "features": [{"name": "system.prompt"}, {"name": "system.router"}, {"name": "system.shortcut"}, {"name": "system.fetch"}, {"name": "system.nfc"}, {"name": "system.clipboard"}, {"name": "system.sensor"}], "permissions": [{"origin": "*"}], "template/official": "demo-template", "config": {"logLevel": "debug", "requestNotificationPermission": false}, "router": {"entry": "pages/Flash", "pages": {"pages/Flash": {"component": "index"}, "pages/Splash": {"launchMode": "singleTask", "component": "index"}, "pages/Home": {"component": "index"}, "pages/Web": {"component": "index"}, "pages/List": {"component": "index"}, "pages/Service": {"component": "index"}, "pages/Content/Introduce": {"component": "index"}, "pages/Content/Detail": {"component": "index"}, "pages/Content/Reader": {"component": "index"}, "UnionAd/AdLanding": {"component": "index"}, "UnionAd/AdReward": {"component": "index"}}}, "display": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "themeMode": 0, "menuBarData": {"menuBar": false}, "menu": true, "pages": {"pages/Home": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "menuBarData": {"menuBar": false}, "fullScreen": false}, "pages/Content/Introduce": {"titleBarText": "简介", "statusBarImmersive": true, "fullScreen": false, "titleBarBackgroundColor": "#000000", "titleBarTextColor": "#ffffff", "menu": true}, "pages/Content/Detail": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Content/Reader": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": true}, "pages/Web": {"titleBarBackgroundColor": "#f2f2f2", "titleBarTextColor": "#414141", "menu": true}, "pages/Flash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "menu": true, "fullScreen": false}, "pages/Splash": {"titleBarText": "", "titleBar": false, "statusBarImmersive": true, "statusBarBackgroundOpacity": 0, "fitCutout": "portrait", "fullScreen": true, "menu": true}, "pages/Service": {"titleBarText": "在线客服", "menu": true}, "pages/List": {"titleBarBackgroundColor": "#000000", "titleBarTextColor": "#ffffff", "menu": true}, "UnionAd/AdReward": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}, "UnionAd/AdLanding": {"fullScreen": true, "titleBar": false, "statusBarImmersive": true, "menu": false, "menuBarData": {"menuBar": false}}}}}