<!-- <import name="nativead" src="@quickapp/business/lib/nativead.ux"></import> -->
<import name="apex-popup" src="@quickapp/apex-ui/components/popup/index"></import>
<import name="chapter-list" src="../../../components/chapter-list"></import>

<template>
  <div id="drawer" class="page">
    <div class="title-box">
      <image class="cover" src="{{info.cover}}"></image>
      <div class='title-left'>
        <text class="title">{{ info.name }}</text>
        <text class="category">{{ info.category }}</text>
        <text class="chapterCount">{{ info.chapterCount }}卷</text>

      </div>
    </div>
    <text class="introduction">{{ info.introduction }}</text>

    <div class="ad-box">
      <!-- <nativead id="nativead" event-name="ad_essay_synopsis"></nativead> -->
    </div>

    <div class="chapter-box">
      <chapter-list
        book-id="{{id}}"
        @click-item="handleClickItem"
      ></chapter-list>
    </div>
    <div class="footer">
      <input
        show="{{showAddBookShelf}}"
        class="add-bookshelf"
        type="button"
        value="加入书架"
        @click="addBookShelf"
      />
      <input class="read" type="button" value="马上阅读" @click="toDetail" />
    </div>
  </div>
</template>

<script>
import { addBookShelf, getBookInfoData } from '@/api/comics'
import router from '@system.router'
import { showToast } from '@quickapp/utils'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    info: {
      author: '悦忱',
      category: '热血兵王',
      chapterCount: 1460,
      cover:
        'http://img.1391.com/api/v1/bookcenter/cover/1/3369879/3369879_750203615b7045148a9db83cecc3e002.jpg',
      id: 1,
      introduction:
        '「都市+爽文+热血」狂尊归来，我为主宰，恩仇必报，逆者必杀，天逆弑天，神逆弑神！狂尊归来战都市，了却恩怨情仇事！四年腥风血雨路，绝弑狂尊为谁戮？我自狂傲逆天行，吾名景昊弑神冥！',
      name: '都市绝弑狂尊',
      tags: '兵王,城市,异术超能,异能,热血兵王,爽文,美女,都市',
      words: 3358878,
      firstChapterId: null,
      isInShelf: false,
      chapterId: null, // 读到到章节
    },
    showDraw: false,
    showAddBookShelf: true,
  },

  protected: {
    id: 1, // 小说id
  },

  onShow() {
    try {
      this.$child('nativead').reportNativeShow()
    } catch (e) {}
    getBookInfoData({ id: this.id }).then(res => {
      if (res) {
        this.info = res
        this.showAddBookShelf = !res.isInShelf
      }
    })
  },

  toDetail() {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: this.id,
        chapterId: this.info.chapterId || this.info.firstChapterId,
      },
    })
  },

  handleClickItem({ detail }) {
    router.push({
      uri: 'pages/Content/Detail',
      params: {
        bookId: detail.bookId,
        chapterId: detail.id,
      },
    })
  },

  addBookShelf() {
    addBookShelf([this.info.id])
      .then(() => {
        showToast('成功加入书架')
        this.showAddBookShelf = false
      })
      .catch(() => {
        showToast('失败加入书架')
      })
  },
})
</script>

<style lang="less">
.page {
  flex-direction: column;
  padding-bottom: 188px;
  background-color: #FEFEFE;
}

.ad-box {
  border-radius: 20px;
  margin-top: 40px;
  justify-content: center;
}

.title-box {
  margin: 30px;

  .cover {
    width: 240px;
    height: 324px;
    border-radius: 20px;
    border: 4px solid #F7F7F7;
  }

  .title-left {
    flex-direction: column;
    flex: 1;
    margin-top: 40px;
    margin-left: 40px;
  }

  .title {
    font-size: 40px;
    font-weight: bold;
    color: #333333;
  }

  .category {
    padding: 0 10px;
    height: 44px;
    font-size: 28px;
    color: #02FFCA;
    background-color: rgba(255,255,255,0.2);
    border-radius: 8px;
  }
}

.introduction {
  font-size: 28px;
  margin: 0 30px;
  color: #565656;
  line-height: 42px;
  //background-color: #F7F7F8;
  padding: 24px;
  border-radius: 32px;
}

.chapter-box {
  background-color: white;
  padding: 0 32px;
  border-radius: 32px;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750px;
  height: 168px;
  padding-top: 32px;
  padding-right: 32px;
  padding-left: 32px;
  justify-content: flex-end;
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0px -2px 6px 0px #e8e8e8;
  backdrop-filter: blur(10px);
  border-top: 1px solid #eee;

  input {
    text-align: center;
    width: 240px;
    height: 80px;
    color: black;
    font-size: 28px;
    font-weight: bold;
    border-radius: 40px;
    border: 2px solid #000000;
    background-color: white;
  }

  .add-bookshelf {
    margin-right: 30px;
  }

  .read {
    background-color: #64E1C7;
  }
}
</style>
