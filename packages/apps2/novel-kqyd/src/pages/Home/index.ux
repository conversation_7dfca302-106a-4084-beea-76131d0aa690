<import name="bookstore-page" src="../../components/BookstorePage.ux"></import>
<import name="bookshelf-page" src="../../components/BookshelfPage.ux"></import>
<!-- <import name="intad" src="@quickapp/business/lib/intad.ux"></import> -->
<import name="service" src="@quickapp/mc-ui/components/service.ux"></import>
<import
  name="mine-page"
  src="@quickapp/mc-ui/components/setting-page.ux"
></import>

<template>
  <div>
    <!-- <intad
      id="intad"
      event-name="ad_home_insert"
      @close="handleAdClose"
    ></intad> -->
    <tabs class="wrapper" @change="handleChange" index="{{currentIndex}}">
      <tab-content class="content">
        <div class="content-box" for="item in pageList" tid="$idx">
          <component
            is="{{item.pageComponent}}"
            index="{{$idx}}"
            current-index="{{currentIndex}}"
          ></component>
        </div>
      </tab-content>
      <tab-bar class="tab-bar">
        <div for="item in pageList" tid="$idx" class="tab-bar-item">
          <image
            if="{{currentIndex === $idx}}"
            class="tab-bar-icon-active"
            src="{{item.selectedIconPath}}"
          />
          <image else class="tab-bar-icon" src="{{item.iconPath}}" />
          <text class="tab-text">
            {{ item.text }}
          </text>
        </div>
      </tab-bar>
    </tabs>
    <service></service>
  </div>
</template>

<script>
import { trackEvent } from '@quickapp/business'
import device from '@quickapp/business/lib/device'
import { toServiceUrl2 } from '@quickapp/utils'
import { initHandle, protectedObj } from '@quickapp/utils/lib/toReaderPage'
import { setPageMenuConfig } from '@quickapp/utils/lib/setPageMenuConfig'

export default setPageMenuConfig({
  private: {
    pageList: [
      {
        iconPath: '/assets/images/ic_bookstore.webp',
        selectedIconPath: '/assets/images/ic_bookstore_active.webp',
        pageComponent: 'bookstore-page',
        text: '漫画',
      },
      {
        iconPath: '/assets/images/ic_bookshelf.webp',
        selectedIconPath: '/assets/images/ic_bookshelf_active.webp',
        pageComponent: 'bookshelf-page',
        text: '书架',
      },
      {
        iconPath: '/assets/images/ic_mine.webp',
        selectedIconPath: '/assets/images/ic_mine_active.webp',
        pageComponent: 'mine-page',
        text: '我的',
      },
    ],
    currentIndex: 0,
    rewardCount: 0,
  },

  protected: {
    ...protectedObj,
  },

  onInit() {
    initHandle.bind(this)()
  },

  onShow() {
    trackEvent({
      category: 'page',
      action: 'show',
      opt_label: 'main',
    })
    // this.$child('intad').handleShow()
  },
  onBackPress() {
    return false
  },
  handleAdClose() {
    let intad = this.$child('intad')
    if (intad && intad.showBackAd) {
      intad.showBackAd()
    }
  },

  handleChange(evt) {
    this.currentIndex = evt.index
    this.$page.setTitleBar({
      text: this.pageList[evt.index].text,
    })
  },

  toServicePage() {
    toServiceUrl2()
  },
})
</script>

<style lang="less">
@import '../../assets/styles/style.less';

.wrapper {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.tab-bar {
  display: flex;
  width: 100%;
  height: 135px;
  background-color: white;
}

.center-tab {
  justify-content: center;
  align-items: center;
  background-color: #fbdf4c;
  width: 60px;
  height: 60px;
  border-radius: 50%;

  .icon {
    width: 35px;
    height: 35px;
  }
}

.tab-bar-item {
  width: 20%;
  flex-direction: column;
  align-items: center;
  height: 100px;
  background-color: white;

  .tab-bar-icon {
    width: 52px;
    height: 52px;
  }

  .tab-bar-icon-active {
    width: 52px;
    height: 52px;
  }

  .tab-text {
    text-align: center;
    color: #8994a3;
  }

  .tab-text:active {
    color: #000;
  }
}

.service-box {
  flex-direction: column;
  position: absolute;
  left: 48px;
  bottom: 150px;

  image {
    width: 132px;
    height: 132px;
  }
}
</style>
