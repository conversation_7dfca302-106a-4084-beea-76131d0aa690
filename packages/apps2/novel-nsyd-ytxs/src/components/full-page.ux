<template>
  <div class="wrapper" style="{{wrapperStyle}}">
    <slot></slot>
  </div>
</template>

<script>
export default {
  data: {
  },

  props: ["background-color", "position"],

  computed: {
    wrapperStyle() {
      return {
        width: this.$page.windowWidth + 'px',
        height: this.$page.windowHeight + 'px',
        backgroundColor: this.backgroundColor,
        position: this.position || 'relative'
      }
    }
  },
}
</script>
