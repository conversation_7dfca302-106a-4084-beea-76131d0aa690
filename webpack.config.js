const path = require('path')
const webpack = require('webpack')
const JsConfuserWebpackPlugin = require('./scripts/js-confuser-webpack-plugin')
const env = process.env.NODE_ENV

module.exports = modulePath => {
  return {
    resolve: {
      alias: {
        '@': path.resolve(modulePath, 'src'),
      },
    },
    plugins: [
      // 自动替换代码中的变量
      new webpack.DefinePlugin({
        __MANIFEST__: JSON.stringify(
          require(path.resolve(modulePath, 'src/manifest.json'))
        ),
        'process.env.NODE_ENV': `'${env}'`,
      }),
      ...(env === 'production'
        ? [
            new JsConfuserWebpackPlugin(
              {
                target: 'browser',
                preset: 'low',
                stringConcealing: true,
                customStringEncodings: [
                  {
                    code: `
            function {fnName}(str){
              let newStr = ''
                      for (let i = 0; i < str.length; i++) {
                        newStr += String.fromCharCode(
                          str.charCodeAt(i) ^ 0x1234
                        )
                      }
                      return newStr
            }`,
                    encode: str => {
                      let newStr = ''
                      for (let i = 0; i < str.length; i++) {
                        newStr += String.fromCharCode(
                          str.charCodeAt(i) ^ 0x1234
                        )
                      }
                      return newStr
                    },
                  },
                ],
                identifierGenerator: 'mangled',
              },
              ['app.js', 'pages/Splash/index.js']
            ),
          ]
        : []),
    ],
  }
}
