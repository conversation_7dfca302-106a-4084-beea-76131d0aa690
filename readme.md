## 前端项目

### 安装

```shell
npm install && npm run init	
```

### 安装报错
当使用默认的--npm-client，lerna bootstrap将调用npm ci而非在 CI 环境中调用npm install。若要禁用此行为，可以使用--no-ci:
```shell
lerna bootstrap --no-ci
```
若要在本地安装期间强制执行(在本地安装中不会自动启用)，请使用--ci：
```shell
lerna bootstrap --ci
```
这对于“干净的”重新安装或重新克隆后的初次安装非常有用。

npm ci 和 npm install 都是安装模块；但是 npm ci 需要 package-lock.json 或者 npm-shrinkwrap.json.
文档: [https://docs.npmjs.com/cli/v8/commands/npm-ci]()


### 启动

使用快应用工具打开对应的项目

### 打包

使用快应用工具打包对应的项目


### 签名
```shell
openssl req -newkey rsa:2048 -nodes -keyout private.pem -x509 -days 3650 -out certificate.pem
```
将私钥文件 private.pem 和证书文件 certificate.pem 拷贝进工程的 sign 目录下

### lerna的依赖提升

把相同依赖提升至顶级[出过一次bug不知道啥问题,可能是npm ci的问题]

```shell
lerna bootstrap --hoist
```

### 新建项目
```shell
lerna create <name>
```

### 运行脚本
运行对应项目的脚本，一定要加上 --stream(交叉并行输出结果),否则控制台没有任何输出就很懵逼
```shell
lerna run dev --stream --scope=web
```

### 添加依赖
添加依赖至对应的项目
```shell
lerna add xxx --scope=xxx
```

### 注意点

Package.json文件中的name字段要与文件夹名相同，否则lerna会提示找不到对应的包

### 执行脚本
```shell
lerna exec --scope clpp -- yarn dev
lerna exec --scope react-components -- yarn build
```

### 查看日志
```shell
adb logcat -s LOGCAT_CONSOLE
```

### 打包查看日子
```shell
adb shell tail -f /sdcard/Android/data/com.huawei.fastapp.dev/files/fastappEngine/com.gcbz.wallpaper/log.txt
```

### 快手链接
```
https://hapjs.org/app/com.dayrepeat.answer?auto_agree=1&clickid=shiwan_kuaishou&backurl=__CALLBACK__
hap://app/com.dayrepeat.answer/pages/Flash?auto_agree=1&clickid=shiwan_kuaishou&backurl=__CALLBACK__
```

### 分享链接
```
沐码小说
https://user.quickapp.cn/?packageName=com.mmxs.novel&path=&shareUrl=&params=
```