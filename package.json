{"name": "root", "private": true, "workspaces": ["packages/apps/*", "packages/modules/*"], "scripts": {"build": "node scripts/build.js"}, "devDependencies": {"@types/quickapp": "npm:quickapp-interface@^1.0.0", "fs-extra": "^11.3.0", "js-confuser": "^2.0.0", "lerna": "^8.1.9"}, "prettier": {"printWidth": 80, "tabWidth": 2, "semi": false, "singleQuote": true, "bracketSpacing": true, "trailingComma": "es5", "arrowParens": "avoid", "htmlWhitespaceSensitivity": "ignore"}, "dependencies": {}}